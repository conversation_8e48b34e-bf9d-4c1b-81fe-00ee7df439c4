/**
 * @file mcu_uart_api.c
 * @brief MCU UART增强版统一API实现
 * @details 提供高层次的API接口实现
 * @version 1.0.0
 * @date 2025-01-19
 */

#include "mcu_uart_api.h"
#include "mcu_uart_commands.h"
#include <stdlib.h>

/* ========== 全局变量 ========== */

/** @brief 全局句柄指针(用于兼容性) */
static mcu_uart_handle_t *g_mcu_uart_handle = NULL;

/* ========== 内部函数声明 ========== */

static mcu_uart_error_t mcu_uart_init_modules(mcu_uart_handle_t *handle);
static mcu_uart_error_t mcu_uart_deinit_modules(mcu_uart_handle_t *handle);
static void mcu_uart_set_state(mcu_uart_handle_t *handle, mcu_uart_device_state_t new_state);
static mcu_uart_error_t mcu_uart_validate_handle(const mcu_uart_handle_t *handle);

/* ========== 主要API实现 ========== */

/**
 * @brief 创建MCU UART句柄
 */
mcu_uart_handle_t* mcu_uart_create_handle(void)
{
    mcu_uart_handle_t *handle = NULL;
    
    /* 分配内存 */
    handle = (mcu_uart_handle_t*)malloc(sizeof(mcu_uart_handle_t));
    if (!handle) {
        return NULL;
    }
    
    /* 初始化为零 */
    memset(handle, 0, sizeof(mcu_uart_handle_t));
    
    /* 设置魔数 */
    handle->magic = MCU_UART_MAGIC_HANDLE;
    
    /* 设置初始状态 */
    handle->state = MCU_UART_DEVICE_STATE_UNINITIALIZED;
    handle->init_timestamp = ktime_get();
    
    return handle;
}

/**
 * @brief 销毁MCU UART句柄
 */
mcu_uart_error_t mcu_uart_destroy_handle(mcu_uart_handle_t *handle)
{
    if (!MCU_UART_IS_VALID_HANDLE(handle)) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 确保已经反初始化 */
    if (handle->state != MCU_UART_DEVICE_STATE_UNINITIALIZED) {
        mcu_uart_deinit(handle);
    }
    
    /* 清除魔数 */
    handle->magic = 0;
    
    /* 释放内存 */
    free(handle);
    
    /* 清除全局句柄 */
    if (g_mcu_uart_handle == handle) {
        g_mcu_uart_handle = NULL;
    }
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 初始化MCU UART驱动
 */
mcu_uart_error_t mcu_uart_init(mcu_uart_handle_t *handle, const mcu_uart_init_config_t *config)
{
    mcu_uart_error_t ret = MCU_UART_ERROR_NONE;
    
    if (!MCU_UART_IS_VALID_HANDLE(handle) || !config) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (handle->state != MCU_UART_DEVICE_STATE_UNINITIALIZED) {
        return MCU_UART_ERROR_ALREADY_INITIALIZED;
    }
    
    /* 设置状态为初始化中 */
    mcu_uart_set_state(handle, MCU_UART_DEVICE_STATE_INITIALIZING);
    
    /* 初始化全局配置 */
    mcu_uart_config_init_default(&handle->config);
    
    /* 应用用户配置 */
    handle->config.device_type = config->device_type;
    handle->local_addr = config->device_address;
    
    /* 硬件配置 */
    if (config->uart3_path) {
        handle->config.hardware.uart3_path = config->uart3_path;
    }
    if (config->uart1_path) {
        handle->config.hardware.uart1_path = config->uart1_path;
    }
    handle->config.hardware.uart3_baudrate = config->uart3_baudrate;
    handle->config.hardware.uart1_baudrate = config->uart1_baudrate;
    
    /* 缓冲区配置 */
    handle->config.buffer.rx_buffer_size = config->rx_buffer_size;
    handle->config.buffer.tx_buffer_size = config->tx_buffer_size;
    handle->config.buffer.cmd_queue_size = config->cmd_queue_size;
    
    /* 协议配置 */
    handle->config.protocol.enable_ack_wait = config->enable_ack;
    handle->config.protocol.enable_4byte_address = config->enable_4byte_addr;
    handle->config.protocol.ack_timeout_ms = config->ack_timeout_ms;
    handle->config.protocol.max_resend_count = config->max_retry_count;
    
    /* 调试配置 */
    handle->config.debug.enable_recv_data_print = config->enable_debug;
    handle->config.debug.enable_send_data_print = config->enable_debug;
    handle->config.debug.enable_performance_monitor = config->enable_stats;
    
    /* 性能配置 */
    handle->config.performance.enable_async_processing = config->enable_async_processing;
    handle->config.hardware.enable_flow_control = config->enable_flow_control;
    
    /* 回调函数 */
    handle->data_handler = config->data_handler;
    handle->error_handler = config->error_handler;
    handle->command_handler = config->command_handler;
    handle->state_handler = config->state_handler;
    handle->callback_context = config->callback_context;
    
    /* 初始化各个模块 */
    ret = mcu_uart_init_modules(handle);
    if (ret != MCU_UART_ERROR_NONE) {
        mcu_uart_set_state(handle, MCU_UART_DEVICE_STATE_ERROR);
        return ret;
    }
    
    /* 设置全局句柄(用于兼容性) */
    g_mcu_uart_handle = handle;
    
    /* 设置状态为就绪 */
    mcu_uart_set_state(handle, MCU_UART_DEVICE_STATE_READY);
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 反初始化MCU UART驱动
 */
mcu_uart_error_t mcu_uart_deinit(mcu_uart_handle_t *handle)
{
    if (!MCU_UART_IS_VALID_HANDLE(handle)) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 停止驱动 */
    if (handle->state == MCU_UART_DEVICE_STATE_RUNNING) {
        mcu_uart_stop(handle);
    }
    
    /* 反初始化各个模块 */
    mcu_uart_deinit_modules(handle);
    
    /* 设置状态为未初始化 */
    mcu_uart_set_state(handle, MCU_UART_DEVICE_STATE_UNINITIALIZED);
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 启动MCU UART驱动
 */
mcu_uart_error_t mcu_uart_start(mcu_uart_handle_t *handle)
{
    mcu_uart_error_t ret = MCU_UART_ERROR_NONE;
    
    if (!MCU_UART_IS_VALID_HANDLE(handle)) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (handle->state != MCU_UART_DEVICE_STATE_READY) {
        return MCU_UART_ERROR_DEVICE_NOT_READY;
    }
    
    /* 打开传输通道 */
    ret = mcu_uart_transport_open(&handle->transport_main);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    ret = mcu_uart_transport_open(&handle->transport_remote);
    if (ret != MCU_UART_ERROR_NONE) {
        mcu_uart_transport_close(&handle->transport_main);
        return ret;
    }
    
    /* 启动处理线程 */
    handle->thread_running = true;
    
    /* 设置状态为运行中 */
    mcu_uart_set_state(handle, MCU_UART_DEVICE_STATE_RUNNING);
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 停止MCU UART驱动
 */
mcu_uart_error_t mcu_uart_stop(mcu_uart_handle_t *handle)
{
    if (!MCU_UART_IS_VALID_HANDLE(handle)) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (handle->state != MCU_UART_DEVICE_STATE_RUNNING) {
        return MCU_UART_ERROR_NONE; /* 已经停止 */
    }
    
    /* 停止处理线程 */
    handle->thread_running = false;
    
    /* 关闭传输通道 */
    mcu_uart_transport_close(&handle->transport_main);
    mcu_uart_transport_close(&handle->transport_remote);
    
    /* 设置状态为就绪 */
    mcu_uart_set_state(handle, MCU_UART_DEVICE_STATE_READY);
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 暂停MCU UART驱动
 */
mcu_uart_error_t mcu_uart_suspend(mcu_uart_handle_t *handle)
{
    if (!MCU_UART_IS_VALID_HANDLE(handle)) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (handle->state != MCU_UART_DEVICE_STATE_RUNNING) {
        return MCU_UART_ERROR_DEVICE_NOT_READY;
    }
    
    /* 设置状态为挂起 */
    mcu_uart_set_state(handle, MCU_UART_DEVICE_STATE_SUSPENDED);
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 恢复MCU UART驱动
 */
mcu_uart_error_t mcu_uart_resume(mcu_uart_handle_t *handle)
{
    if (!MCU_UART_IS_VALID_HANDLE(handle)) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (handle->state != MCU_UART_DEVICE_STATE_SUSPENDED) {
        return MCU_UART_ERROR_DEVICE_NOT_READY;
    }
    
    /* 设置状态为运行中 */
    mcu_uart_set_state(handle, MCU_UART_DEVICE_STATE_RUNNING);
    
    return MCU_UART_ERROR_NONE;
}

/* ========== 数据传输API实现 ========== */

/**
 * @brief 发送数据
 */
mcu_uart_error_t mcu_uart_send_data(
    mcu_uart_handle_t *handle,
    const __u8 *data,
    __u32 length,
    bool wait_ack)
{
    mcu_uart_error_t ret = MCU_UART_ERROR_NONE;
    __u8 packet_buffer[MCU_UART_MAX_PACKET_LEN];
    __u32 packet_size = sizeof(packet_buffer);
    __u32 bytes_sent = 0;
    
    if (!MCU_UART_IS_VALID_HANDLE(handle) || !data || length == 0) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (!MCU_UART_IS_RUNNING_HANDLE(handle)) {
        return MCU_UART_ERROR_DEVICE_NOT_READY;
    }
    
    /* 构建数据包 */
    ret = mcu_uart_protocol_build_packet(0, data, length, packet_buffer, &packet_size);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    /* 发送数据包 */
    ret = mcu_uart_transport_send(&handle->transport_main, packet_buffer, packet_size, &bytes_sent);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    /* 更新统计 */
    handle->total_bytes_transferred += bytes_sent;
    handle->last_activity_timestamp = ktime_get();
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 发送命令
 */
mcu_uart_error_t mcu_uart_send_command(
    mcu_uart_handle_t *handle,
    __u16 command_id,
    const __u8 *data,
    __u32 length)
{
    mcu_uart_error_t ret = MCU_UART_ERROR_NONE;
    __u8 packet_buffer[MCU_UART_MAX_PACKET_LEN];
    __u32 packet_size = sizeof(packet_buffer);
    __u32 bytes_sent = 0;
    
    if (!MCU_UART_IS_VALID_HANDLE(handle)) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (!MCU_UART_IS_RUNNING_HANDLE(handle)) {
        return MCU_UART_ERROR_DEVICE_NOT_READY;
    }
    
    /* 构建命令包 */
    ret = mcu_uart_protocol_build_packet(command_id, data, length, packet_buffer, &packet_size);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    /* 发送命令包 */
    ret = mcu_uart_transport_send(&handle->transport_main, packet_buffer, packet_size, &bytes_sent);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    /* 更新统计 */
    handle->total_commands_processed++;
    handle->total_bytes_transferred += bytes_sent;
    handle->last_activity_timestamp = ktime_get();
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 发送远程命令
 */
mcu_uart_error_t mcu_uart_send_remote_command(
    mcu_uart_handle_t *handle,
    __u16 command_id,
    mcu_uart_device_addr_t target_addr,
    __u8 zone_mask,
    const __u8 *data,
    __u32 length,
    bool ack_required)
{
    mcu_uart_error_t ret = MCU_UART_ERROR_NONE;
    __u8 packet_buffer[MCU_UART_MAX_PACKET_LEN];
    __u32 packet_size = sizeof(packet_buffer);
    __u32 bytes_sent = 0;
    
    if (!MCU_UART_IS_VALID_HANDLE(handle)) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (!MCU_UART_IS_RUNNING_HANDLE(handle)) {
        return MCU_UART_ERROR_DEVICE_NOT_READY;
    }
    
    /* 构建远程命令包 */
    ret = mcu_uart_protocol_build_remote_packet(
        command_id, target_addr, zone_mask, ack_required,
        data, length, packet_buffer, &packet_size);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    /* 发送到远程通道 */
    ret = mcu_uart_transport_send(&handle->transport_remote, packet_buffer, packet_size, &bytes_sent);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    /* 更新统计 */
    handle->total_commands_processed++;
    handle->total_bytes_transferred += bytes_sent;
    handle->last_activity_timestamp = ktime_get();
    
    return MCU_UART_ERROR_NONE;
}

/* ========== 内部函数实现 ========== */

/**
 * @brief 初始化各个模块
 */
static mcu_uart_error_t mcu_uart_init_modules(mcu_uart_handle_t *handle)
{
    mcu_uart_error_t ret = MCU_UART_ERROR_NONE;
    
    /* 初始化缓冲区管理器 */
    ret = mcu_uart_buffer_manager_init(&handle->buffer_manager, &handle->config.buffer);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    /* 初始化传输层 */
    ret = mcu_uart_transport_init(&handle->transport_main, MCU_UART_CHANNEL_MAIN, &handle->config.hardware);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    ret = mcu_uart_transport_init(&handle->transport_remote, MCU_UART_CHANNEL_REMOTE, &handle->config.hardware);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    /* 初始化协议处理器 */
    ret = mcu_uart_protocol_init(&handle->protocol, &handle->config.protocol);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    /* 初始化命令队列 */
    ret = mcu_uart_command_queue_init(&handle->command_queue, handle->config.buffer.cmd_queue_size, true);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    /* 初始化命令分发器 */
    ret = mcu_uart_command_dispatcher_init(&handle->dispatcher, 32);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 反初始化各个模块
 */
static mcu_uart_error_t mcu_uart_deinit_modules(mcu_uart_handle_t *handle)
{
    /* 反初始化命令分发器 */
    mcu_uart_command_dispatcher_deinit(&handle->dispatcher);
    
    /* 反初始化命令队列 */
    mcu_uart_command_queue_deinit(&handle->command_queue);
    
    /* 反初始化协议处理器 */
    mcu_uart_protocol_deinit(&handle->protocol);
    
    /* 反初始化传输层 */
    mcu_uart_transport_deinit(&handle->transport_remote);
    mcu_uart_transport_deinit(&handle->transport_main);
    
    /* 反初始化缓冲区管理器 */
    mcu_uart_buffer_manager_deinit(&handle->buffer_manager);
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 设置设备状态
 */
static void mcu_uart_set_state(mcu_uart_handle_t *handle, mcu_uart_device_state_t new_state)
{
    mcu_uart_device_state_t old_state = handle->state;
    
    if (old_state != new_state) {
        handle->state = new_state;
        
        /* 调用状态变化回调 */
        if (handle->state_handler) {
            handle->state_handler(old_state, new_state, handle->callback_context);
        }
    }
}

/**
 * @brief 验证句柄有效性
 */
static mcu_uart_error_t mcu_uart_validate_handle(const mcu_uart_handle_t *handle)
{
    if (!handle) {
        return MCU_UART_ERROR_NULL_POINTER;
    }
    
    if (handle->magic != MCU_UART_MAGIC_HANDLE) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    return MCU_UART_ERROR_NONE;
}

/* ========== 向前兼容性API实现 ========== */

/**
 * @brief 兼容原有的初始化函数
 */
mcu_uart_error_t mcu_interrupt_process_init(void)
{
    mcu_uart_init_config_t config = {
        .device_type = MCU_UART_DEVICE_HEAD_UNIT,
        .device_address = 0x0000,
        .uart3_path = "b:\\BUS\\UART3",
        .uart1_path = "b:\\BUS\\UART1",
        .uart3_baudrate = 115200,
        .uart1_baudrate = 115200,
        .rx_buffer_size = MCU_UART_RX_BUFFER_SIZE,
        .tx_buffer_size = MCU_UART_TX_BUFFER_SIZE,
        .cmd_queue_size = MCU_UART_MAX_CMD_NUMBER,
        .enable_ack = true,
        .enable_4byte_addr = false,
        .ack_timeout_ms = MCU_UART_ACK_TIMEOUT_MS,
        .max_retry_count = MCU_UART_RESEND_COUNT,
        .enable_debug = false,
        .enable_stats = true,
        .enable_async_processing = true,
        .enable_flow_control = false,
        .data_handler = NULL,
        .error_handler = NULL,
        .command_handler = NULL,
        .state_handler = NULL,
        .callback_context = NULL
    };
    
    /* 创建全局句柄 */
    if (!g_mcu_uart_handle) {
        g_mcu_uart_handle = mcu_uart_create_handle();
        if (!g_mcu_uart_handle) {
            return MCU_UART_ERROR_NO_MEMORY;
        }
    }
    
    /* 初始化 */
    return mcu_uart_init(g_mcu_uart_handle, &config);
}

/**
 * @brief 兼容原有的发送命令函数
 */
__s32 uart_send_cmd_data(__u16 cmd, __u8 *pData)
{
    if (!g_mcu_uart_handle) {
        return -1;
    }
    
    /* 计算数据长度(简化处理) */
    __u32 data_len = 0;
    if (pData) {
        /* 根据命令类型确定数据长度 */
        data_len = 4; /* 默认长度 */
    }
    
    mcu_uart_error_t ret = mcu_uart_send_command(g_mcu_uart_handle, cmd, pData, data_len);
    return (ret == MCU_UART_ERROR_NONE) ? 0 : -1;
}

/**
 * @brief 兼容原有的子发送命令函数
 */
__s32 uart_sub_send_cmd_data(__u16 cmd, __u8 *pData)
{
    if (!g_mcu_uart_handle) {
        return -1;
    }
    
    /* 计算数据长度(简化处理) */
    __u32 data_len = 0;
    if (pData) {
        data_len = 4; /* 默认长度 */
    }
    
    mcu_uart_error_t ret = mcu_uart_send_remote_command(
        g_mcu_uart_handle, cmd, MCU_UART_ALL_REMOTE_DEVICE, 0xFF, pData, data_len, false);
    return (ret == MCU_UART_ERROR_NONE) ? 0 : -1;
}
