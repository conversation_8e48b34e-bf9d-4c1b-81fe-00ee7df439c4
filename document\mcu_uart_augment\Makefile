# MCU UART增强版 Makefile
# 版本: 1.0.0
# 日期: 2025-01-19

# ========== 项目配置 ==========

PROJECT_NAME = mcu_uart_augment
VERSION = 1.0.0
BUILD_DATE = $(shell date +"%Y-%m-%d %H:%M:%S")

# ========== 目录配置 ==========

SRC_DIR = src
INC_DIR = include
TEST_DIR = test
BUILD_DIR = build
OBJ_DIR = $(BUILD_DIR)/obj
BIN_DIR = $(BUILD_DIR)/bin
LIB_DIR = $(BUILD_DIR)/lib

# ========== 编译器配置 ==========

# 使用RISC-V GCC工具链
CC = riscv64-unknown-elf-gcc
AR = riscv64-unknown-elf-ar
OBJCOPY = riscv64-unknown-elf-objcopy
SIZE = riscv64-unknown-elf-size

# 如果RISC-V工具链不可用，回退到标准GCC
ifeq ($(shell which $(CC) 2>/dev/null),)
    CC = gcc
    AR = ar
    OBJCOPY = objcopy
    SIZE = size
    $(warning RISC-V toolchain not found, using standard GCC)
endif

# ========== 编译选项 ==========

# 基础编译选项
CFLAGS = -std=c99 -Wall -Wextra -Werror
CFLAGS += -I$(INC_DIR)
CFLAGS += -DMCU_UART_VERSION=\"$(VERSION)\"
CFLAGS += -DMCU_UART_BUILD_DATE=\"$(BUILD_DATE)\"

# 调试选项
ifdef DEBUG
    CFLAGS += -g -O0 -DDEBUG=1
    CFLAGS += -DMCU_UART_ENABLE_DEBUG=1
    BUILD_TYPE = debug
else
    CFLAGS += -O2 -DNDEBUG
    BUILD_TYPE = release
endif

# 优化选项
ifdef OPTIMIZE
    CFLAGS += -O3 -flto
    CFLAGS += -DMCU_UART_ENABLE_FAST_PATH=1
    CFLAGS += -DMCU_UART_ENABLE_ZERO_COPY=1
endif

# 兼容性选项
ifdef LEGACY_COMPAT
    CFLAGS += -DMCU_UART_ENABLE_COMPAT=1
    CFLAGS += -DREMOTE_ADDR_4BYTES=1
    CFLAGS += -DACK_WAIT_ENABLE=1
    CFLAGS += -DGET_CMD_NO_ENABLE=1
endif

# 功能开关
ifdef ENABLE_STATS
    CFLAGS += -DMCU_UART_ENABLE_STATS=1
endif

ifdef ENABLE_EXTENSIONS
    CFLAGS += -DMCU_UART_ENABLE_EXTENSIONS=1
endif

# 链接选项
LDFLAGS = -lm

# ========== 源文件配置 ==========

# 核心源文件
CORE_SOURCES = \
    $(SRC_DIR)/mcu_uart_api.c \
    $(SRC_DIR)/mcu_uart_protocol.c \
    $(SRC_DIR)/mcu_uart_buffer.c \
    $(SRC_DIR)/mcu_uart_transport.c \
    $(SRC_DIR)/mcu_uart_command.c \
    $(SRC_DIR)/mcu_uart_config.c

# 测试源文件
TEST_SOURCES = \
    $(TEST_DIR)/mcu_uart_test.c

# 头文件
HEADERS = \
    $(INC_DIR)/mcu_uart_api.h \
    $(INC_DIR)/mcu_uart_types.h \
    $(INC_DIR)/mcu_uart_config.h \
    $(INC_DIR)/mcu_uart_protocol.h \
    $(INC_DIR)/mcu_uart_buffer.h \
    $(INC_DIR)/mcu_uart_transport.h \
    $(INC_DIR)/mcu_uart_command.h \
    $(INC_DIR)/mcu_uart_commands.h

# 对象文件
CORE_OBJECTS = $(CORE_SOURCES:$(SRC_DIR)/%.c=$(OBJ_DIR)/%.o)
TEST_OBJECTS = $(TEST_SOURCES:$(TEST_DIR)/%.c=$(OBJ_DIR)/%.o)

# ========== 目标配置 ==========

# 库文件
STATIC_LIB = $(LIB_DIR)/lib$(PROJECT_NAME).a
SHARED_LIB = $(LIB_DIR)/lib$(PROJECT_NAME).so

# 可执行文件
TEST_EXEC = $(BIN_DIR)/$(PROJECT_NAME)_test

# ========== 构建目标 ==========

.PHONY: all clean test install uninstall help info

# 默认目标
all: $(STATIC_LIB) $(SHARED_LIB) $(TEST_EXEC)
	@echo "Build completed successfully!"
	@echo "Static library: $(STATIC_LIB)"
	@echo "Shared library: $(SHARED_LIB)"
	@echo "Test executable: $(TEST_EXEC)"

# 静态库
$(STATIC_LIB): $(CORE_OBJECTS) | $(LIB_DIR)
	@echo "Creating static library: $@"
	$(AR) rcs $@ $^

# 共享库
$(SHARED_LIB): $(CORE_OBJECTS) | $(LIB_DIR)
	@echo "Creating shared library: $@"
	$(CC) -shared -o $@ $^ $(LDFLAGS)

# 测试可执行文件
$(TEST_EXEC): $(TEST_OBJECTS) $(STATIC_LIB) | $(BIN_DIR)
	@echo "Linking test executable: $@"
	$(CC) -o $@ $^ $(LDFLAGS)

# 编译核心对象文件
$(OBJ_DIR)/%.o: $(SRC_DIR)/%.c $(HEADERS) | $(OBJ_DIR)
	@echo "Compiling: $<"
	$(CC) $(CFLAGS) -c $< -o $@

# 编译测试对象文件
$(OBJ_DIR)/%.o: $(TEST_DIR)/%.c $(HEADERS) | $(OBJ_DIR)
	@echo "Compiling test: $<"
	$(CC) $(CFLAGS) -c $< -o $@

# 创建目录
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

$(OBJ_DIR): | $(BUILD_DIR)
	mkdir -p $(OBJ_DIR)

$(BIN_DIR): | $(BUILD_DIR)
	mkdir -p $(BIN_DIR)

$(LIB_DIR): | $(BUILD_DIR)
	mkdir -p $(LIB_DIR)

# ========== 特殊目标 ==========

# 运行测试
test: $(TEST_EXEC)
	@echo "Running tests..."
	$(TEST_EXEC)

# 单元测试
test-unit: $(TEST_EXEC)
	@echo "Running unit tests..."
	$(TEST_EXEC) --unit

# 集成测试
test-integration: $(TEST_EXEC)
	@echo "Running integration tests..."
	$(TEST_EXEC) --integration

# 性能测试
test-performance: $(TEST_EXEC)
	@echo "Running performance tests..."
	$(TEST_EXEC) --performance

# 内存检查(如果有valgrind)
test-memory: $(TEST_EXEC)
	@if command -v valgrind >/dev/null 2>&1; then \
		echo "Running memory tests with valgrind..."; \
		valgrind --leak-check=full --show-leak-kinds=all $(TEST_EXEC); \
	else \
		echo "Valgrind not found, skipping memory tests"; \
	fi

# 代码覆盖率(如果有gcov)
coverage: CFLAGS += --coverage
coverage: LDFLAGS += --coverage
coverage: clean $(TEST_EXEC)
	@echo "Running coverage tests..."
	$(TEST_EXEC)
	@if command -v gcov >/dev/null 2>&1; then \
		gcov $(CORE_SOURCES); \
		echo "Coverage report generated"; \
	else \
		echo "gcov not found, skipping coverage report"; \
	fi

# 静态分析(如果有cppcheck)
analyze:
	@if command -v cppcheck >/dev/null 2>&1; then \
		echo "Running static analysis..."; \
		cppcheck --enable=all --inconclusive --std=c99 \
			-I$(INC_DIR) $(SRC_DIR) $(TEST_DIR); \
	else \
		echo "cppcheck not found, skipping static analysis"; \
	fi

# 格式化代码(如果有clang-format)
format:
	@if command -v clang-format >/dev/null 2>&1; then \
		echo "Formatting code..."; \
		find $(SRC_DIR) $(INC_DIR) $(TEST_DIR) -name "*.c" -o -name "*.h" | \
			xargs clang-format -i; \
		echo "Code formatted"; \
	else \
		echo "clang-format not found, skipping code formatting"; \
	fi

# 安装
install: $(STATIC_LIB) $(SHARED_LIB)
	@echo "Installing libraries and headers..."
	install -d $(DESTDIR)/usr/local/lib
	install -d $(DESTDIR)/usr/local/include/$(PROJECT_NAME)
	install -m 644 $(STATIC_LIB) $(DESTDIR)/usr/local/lib/
	install -m 755 $(SHARED_LIB) $(DESTDIR)/usr/local/lib/
	install -m 644 $(HEADERS) $(DESTDIR)/usr/local/include/$(PROJECT_NAME)/
	@echo "Installation completed"

# 卸载
uninstall:
	@echo "Uninstalling..."
	rm -f $(DESTDIR)/usr/local/lib/lib$(PROJECT_NAME).*
	rm -rf $(DESTDIR)/usr/local/include/$(PROJECT_NAME)
	@echo "Uninstallation completed"

# 清理
clean:
	@echo "Cleaning build files..."
	rm -rf $(BUILD_DIR)
	rm -f *.gcov *.gcda *.gcno
	@echo "Clean completed"

# 深度清理
distclean: clean
	@echo "Deep cleaning..."
	find . -name "*~" -delete
	find . -name "*.bak" -delete
	find . -name "*.orig" -delete
	@echo "Deep clean completed"

# 项目信息
info:
	@echo "=== $(PROJECT_NAME) Build Information ==="
	@echo "Version: $(VERSION)"
	@echo "Build Date: $(BUILD_DATE)"
	@echo "Build Type: $(BUILD_TYPE)"
	@echo "Compiler: $(CC)"
	@echo "CFLAGS: $(CFLAGS)"
	@echo "LDFLAGS: $(LDFLAGS)"
	@echo "Source Files: $(words $(CORE_SOURCES)) core, $(words $(TEST_SOURCES)) test"
	@echo "Header Files: $(words $(HEADERS))"
	@echo "========================================="

# 帮助信息
help:
	@echo "MCU UART增强版构建系统"
	@echo ""
	@echo "可用目标:"
	@echo "  all              - 构建所有目标(默认)"
	@echo "  test             - 运行所有测试"
	@echo "  test-unit        - 运行单元测试"
	@echo "  test-integration - 运行集成测试"
	@echo "  test-performance - 运行性能测试"
	@echo "  test-memory      - 运行内存测试(需要valgrind)"
	@echo "  coverage         - 生成代码覆盖率报告(需要gcov)"
	@echo "  analyze          - 运行静态分析(需要cppcheck)"
	@echo "  format           - 格式化代码(需要clang-format)"
	@echo "  install          - 安装库和头文件"
	@echo "  uninstall        - 卸载库和头文件"
	@echo "  clean            - 清理构建文件"
	@echo "  distclean        - 深度清理"
	@echo "  info             - 显示构建信息"
	@echo "  help             - 显示此帮助信息"
	@echo ""
	@echo "构建选项:"
	@echo "  DEBUG=1          - 启用调试模式"
	@echo "  OPTIMIZE=1       - 启用优化模式"
	@echo "  LEGACY_COMPAT=1  - 启用兼容模式"
	@echo "  ENABLE_STATS=1   - 启用统计功能"
	@echo "  ENABLE_EXTENSIONS=1 - 启用扩展功能"
	@echo ""
	@echo "示例:"
	@echo "  make DEBUG=1     - 调试模式构建"
	@echo "  make OPTIMIZE=1  - 优化模式构建"
	@echo "  make test        - 运行测试"

# ========== 依赖关系 ==========

# 自动生成依赖关系
-include $(CORE_OBJECTS:.o=.d)
-include $(TEST_OBJECTS:.o=.d)

# 生成依赖文件
$(OBJ_DIR)/%.d: $(SRC_DIR)/%.c | $(OBJ_DIR)
	@$(CC) $(CFLAGS) -MM -MT $(@:.d=.o) $< > $@

$(OBJ_DIR)/%.d: $(TEST_DIR)/%.c | $(OBJ_DIR)
	@$(CC) $(CFLAGS) -MM -MT $(@:.d=.o) $< > $@
