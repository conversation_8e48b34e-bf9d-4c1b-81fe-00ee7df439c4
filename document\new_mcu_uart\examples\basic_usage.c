/**
 * @file basic_usage.c
 * @brief MCU UART通信基础使用示例
 * 
 * 演示如何使用新重构的MCU UART通信模块进行基本的命令发送和接收操作。
 * 包括初始化、配置、发送命令、接收响应等基本流程。
 * 
 * <AUTHOR> UART Team
 * @date 2025-01-XX
 */

#include "mcu_uart.h"
#include <rtthread.h>
#include <stdio.h>

/* ========================================================================== */
/*                             示例配置                                       */
/* ========================================================================== */

/** 示例使用的UART通道 */
#define EXAMPLE_UART_CHANNEL    MCU_UART_CHANNEL_MAIN

/** 示例命令 */
#define EXAMPLE_SYSTEM_INFO_CMD 0x0100  // 系统信息查询命令
#define EXAMPLE_VOLUME_SET_CMD  0x0301  // 音量设置命令

/* ========================================================================== */
/*                           示例函数声明                                     */
/* ========================================================================== */

static void example_init_uart(void);
static void example_send_system_info_query(void);
static void example_send_volume_control(uint8_t volume);
static void example_receive_handler(const mcu_protocol_packet_t *packet, void *user_data);
static void example_cleanup(void);

/* ========================================================================== */
/*                           主示例函数                                       */
/* ========================================================================== */

/**
 * @brief MCU UART基础使用示例
 */
int mcu_uart_basic_example(void)
{
    printf("MCU UART Basic Usage Example\n");
    printf("=============================\n\n");
    
    // 1. 初始化UART通信模块
    printf("1. 初始化MCU UART模块...\n");
    mcu_uart_result_e result = mcu_uart_init();
    if (result != MCU_UART_OK) {
        printf("   错误: 初始化失败 (错误码: %d)\n", result);
        return -1;
    }
    
    // 2. 配置UART参数
    mcu_uart_config_t config;
    mcu_uart_get_default_config(&config);
    config.baudrate = 115200;
    
    // 3. 打开UART通道
    result = mcu_uart_open(MCU_UART_CHANNEL_MAIN, &config);
    if (result != MCU_UART_OK) {
        printf("   错误: 打开UART通道失败 (错误码: %d)\n", result);
        mcu_uart_deinit();
        return -1;
    }
    
    // 4. 启动UART通信
    result = mcu_uart_start(MCU_UART_CHANNEL_MAIN);
    if (result != MCU_UART_OK) {
        printf("   错误: 启动UART通信失败 (错误码: %d)\n", result);
        mcu_uart_close(MCU_UART_CHANNEL_MAIN);
        mcu_uart_deinit();
        return -1;
    }
    
    printf("   MCU UART初始化成功\n");
    
    // 5. 发送测试命令
    printf("2. 发送测试命令...\n");
    
    mcu_command_packet_t cmd_packet;
    mcu_uart_init_command_packet(&cmd_packet);
    
    cmd_packet.command = 0x0100;  // 系统信息查询
    cmd_packet.device_addr = 0x12345678;
    cmd_packet.zone_mask = 0x01;
    cmd_packet.need_ack = true;
    cmd_packet.data_length = 0;
    cmd_packet.data = NULL;
    
    result = mcu_uart_send_command(MCU_UART_CHANNEL_MAIN, &cmd_packet, 5000);
    if (result == MCU_UART_OK) {
        printf("   命令发送成功\n");
    } else {
        printf("   命令发送失败 (错误码: %d)\n", result);
    }
    
    // 6. 清理资源
    printf("3. 清理资源...\n");
    mcu_uart_stop(MCU_UART_CHANNEL_MAIN);
    mcu_uart_close(MCU_UART_CHANNEL_MAIN);
    mcu_uart_deinit();
    
    printf("示例执行完成!\n");
    return 0;
}

/* ========================================================================== */
/*                           示例辅助函数                                     */
/* ========================================================================== */

/**
 * @brief 初始化UART通信
 */
static void example_init_uart(void)
{
    // 初始化MCU UART子系统
    mcu_uart_result_e result = mcu_uart_init();
    if (result != MCU_UART_OK) {
        printf("   错误: 初始化MCU UART子系统失败 (错误码: %d)\n", result);
        return;
    }
    
    // 配置UART参数
    mcu_uart_config_t config;
    mcu_uart_get_default_config(&config);
    config.baudrate = 115200;
    config.enable_flow_control = false;
    config.rx_buffer_size = 16 * 1024;
    config.tx_buffer_size = 4 * 1024;
    
    // 打开UART通道
    result = mcu_uart_open(EXAMPLE_UART_CHANNEL, &config);
    if (result != MCU_UART_OK) {
        printf("   错误: 打开UART通道失败 (错误码: %d)\n", result);
        return;
    }
    
    // 注册接收回调函数
    result = mcu_uart_register_receive_callback(EXAMPLE_UART_CHANNEL, 
                                                 example_receive_handler, 
                                                 NULL);
    if (result != MCU_UART_OK) {
        printf("   警告: 注册接收回调函数失败 (错误码: %d)\n", result);
    }
    
    // 启动UART通信
    result = mcu_uart_start(EXAMPLE_UART_CHANNEL);
    if (result != MCU_UART_OK) {
        printf("   错误: 启动UART通信失败 (错误码: %d)\n", result);
        return;
    }
    
    printf("   MCU UART初始化成功\n");
}

/**
 * @brief 发送系统信息查询命令
 */
static void example_send_system_info_query(void)
{
    // 构造命令包
    mcu_command_packet_t cmd_packet;
    mcu_uart_init_command_packet(&cmd_packet);
    
    cmd_packet.command = EXAMPLE_SYSTEM_INFO_CMD;
    cmd_packet.device_addr = 0x12345678;  // 目标设备地址
    cmd_packet.zone_mask = 0x01;          // 区域掩码
    cmd_packet.need_ack = true;           // 需要ACK确认
    cmd_packet.data_length = 0;           // 无额外数据
    cmd_packet.data = NULL;
    
    // 发送命令
    mcu_uart_result_e result = mcu_uart_send_command(EXAMPLE_UART_CHANNEL, 
                                                      &cmd_packet, 
                                                      5000); // 5秒超时
    
    if (result == MCU_UART_OK) {
        printf("   系统信息查询命令发送成功\n");
    } else {
        printf("   错误: 系统信息查询命令发送失败 (错误码: %d)\n", result);
    }
}

/**
 * @brief 发送音量控制命令
 * 
 * @param volume 音量值 (0-100)
 */
static void example_send_volume_control(uint8_t volume)
{
    // 构造命令包
    mcu_command_packet_t cmd_packet;
    mcu_uart_init_command_packet(&cmd_packet);
    
    cmd_packet.command = EXAMPLE_VOLUME_SET_CMD;
    cmd_packet.device_addr = 0x12345678;
    cmd_packet.zone_mask = 0x01;
    cmd_packet.need_ack = true;
    cmd_packet.data_length = 1;
    cmd_packet.data = &volume;
    
    // 发送命令
    mcu_uart_result_e result = mcu_uart_send_command(EXAMPLE_UART_CHANNEL, 
                                                      &cmd_packet, 
                                                      3000); // 3秒超时
    
    if (result == MCU_UART_OK) {
        printf("   音量设置命令发送成功 (音量: %d%%)\n", volume);
    } else {
        printf("   错误: 音量设置命令发送失败 (错误码: %d)\n", result);
    }
}

/**
 * @brief 接收数据处理回调函数
 * 
 * @param packet 接收到的数据包
 * @param user_data 用户数据
 */
static void example_receive_handler(const mcu_protocol_packet_t *packet, void *user_data)
{
    if (!packet) {
        return;
    }
    
    printf("   收到响应包: 命令=0x%04X, 长度=%u\n", 
           packet->command, packet->data_length);
    
    // 检查是否为ACK响应
    if (packet->command & 0x8000) {
        uint16_t original_cmd = packet->command & 0x7FFF;
        printf("   这是命令0x%04X的ACK响应\n", original_cmd);
        
        if (packet->data_length >= 1) {
            uint8_t ack_result = packet->data[0];
            if (ack_result == 0x00) {
                printf("   ACK结果: 成功\n");
            } else {
                printf("   ACK结果: 失败 (错误码: 0x%02X)\n", ack_result);
            }
        }
    } else {
        // 处理其他类型的响应
        switch (packet->command) {
            case EXAMPLE_SYSTEM_INFO_CMD:
                printf("   系统信息响应数据:\n");
                if (packet->data && packet->data_length > 0) {
                    printf("   ");
                    for (uint32_t i = 0; i < packet->data_length && i < 16; i++) {
                        printf("%02X ", packet->data[i]);
                    }
                    printf("\n");
                }
                break;
                
            default:
                printf("   未知命令响应: 0x%04X\n", packet->command);
                break;
        }
    }
}

/**
 * @brief 清理资源
 */
static void example_cleanup(void)
{
    // 停止UART通信
    mcu_uart_stop(EXAMPLE_UART_CHANNEL);
    
    // 注销接收回调
    mcu_uart_unregister_receive_callback(EXAMPLE_UART_CHANNEL);
    
    // 关闭UART通道
    mcu_uart_close(EXAMPLE_UART_CHANNEL);
    
    // 反初始化MCU UART子系统
    mcu_uart_deinit();
    
    printf("   资源清理完成\n");
}

/* ========================================================================== */
/*                           高级使用示例                                     */
/* ========================================================================== */

/**
 * @brief 异步发送示例
 */
int mcu_uart_async_example(void)
{
    printf("MCU UART Async Usage Example\n");
    printf("============================\n\n");
    
    // 初始化
    example_init_uart();
    
    // 异步发送多个命令
    printf("异步发送多个命令...\n");
    
    for (int i = 0; i < 10; i++) {
        mcu_command_packet_t cmd_packet;
        mcu_uart_init_command_packet(&cmd_packet);
        
        cmd_packet.command = EXAMPLE_VOLUME_SET_CMD;
        cmd_packet.device_addr = 0x12345678;
        cmd_packet.zone_mask = 0x01;
        cmd_packet.need_ack = false; // 异步发送，不等待ACK
        
        uint8_t volume = 10 + i * 9;
        cmd_packet.data_length = 1;
        cmd_packet.data = &volume;
        
        mcu_uart_result_e result = mcu_uart_send_command_async(EXAMPLE_UART_CHANNEL, 
                                                                &cmd_packet);
        
        if (result == MCU_UART_OK) {
            printf("   异步命令 %d 发送成功 (音量: %d%%)\n", i + 1, volume);
        } else {
            printf("   异步命令 %d 发送失败 (错误码: %d)\n", i + 1, result);
        }
        
        rt_thread_mdelay(50); // 短暂延时
    }
    
    // 等待所有命令发送完成
    printf("等待所有命令发送完成...\n");
    rt_thread_mdelay(2000);
    
    // 清理
    example_cleanup();
    
    printf("\n异步示例执行完成!\n");
    return 0;
}

/**
 * @brief 注册到控制台命令
 */
#ifdef RT_USING_FINSH
#include <finsh.h>

FINSH_FUNCTION_EXPORT(mcu_uart_basic_example, MCU UART basic example);
FINSH_FUNCTION_EXPORT(mcu_uart_async_example, MCU UART async usage example);

#ifdef FINSH_USING_MSH
MSH_CMD_EXPORT(mcu_uart_basic_example, MCU UART basic example);
MSH_CMD_EXPORT(mcu_uart_async_example, MCU UART async usage example);
#endif

#endif /* RT_USING_FINSH */ 