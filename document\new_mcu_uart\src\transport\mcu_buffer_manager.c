/**
 * @file mcu_buffer_manager.c
 * @brief MCU UART传输层缓冲区管理器实现
 * @version 1.0
 * @date 2024-01-20
 * 
 * @copyright Copyright (c) 2024 Allwinner Technology Co., Ltd.
 * 
 * @details
 * 缓冲区管理器负责：
 * - 缓冲区内存池管理
 * - 动态缓冲区分配和释放
 * - 缓冲区使用状态监控
 * - 内存碎片整理
 * - 缓冲区统计信息
 */

#include "mcu_uart.h"
#include "internal/mcu_transport_internal.h"
#include "internal/mcu_uart_internal.h"

#include <rtthread.h>
#include <rtdevice.h>
#include <string.h>

/* ================================ 私有宏定义 ================================ */

#define MCU_BUFFER_POOL_MAX_BLOCKS         32
#define MCU_BUFFER_SMALL_BLOCK_SIZE        256
#define MCU_BUFFER_MEDIUM_BLOCK_SIZE       1024
#define MCU_BUFFER_LARGE_BLOCK_SIZE        4096

#define MCU_BUFFER_SMALL_POOL_COUNT        16
#define MCU_BUFFER_MEDIUM_POOL_COUNT       8
#define MCU_BUFFER_LARGE_POOL_COUNT        4

#define MCU_BUFFER_MAGIC_ALLOCATED         0xDEADBEEF
#define MCU_BUFFER_MAGIC_FREE              0xFEEDFACE
#define MCU_BUFFER_GUARD_PATTERN           0x5A

/* ================================ 私有数据结构 ================================ */

/**
 * @brief 缓冲区块头部信息
 */
typedef struct mcu_buffer_block_header {
    uint32_t magic;                             ///< 魔数标识
    uint32_t size;                              ///< 块大小
    uint32_t pool_id;                           ///< 所属池ID
    uint32_t alloc_time;                        ///< 分配时间
    struct mcu_buffer_block_header *next;       ///< 下一个块
    uint8_t guard_start;                        ///< 起始保护字节
} mcu_buffer_block_header_t;

/**
 * @brief 缓冲区块尾部信息
 */
typedef struct mcu_buffer_block_footer {
    uint8_t guard_end;                          ///< 结束保护字节
    uint32_t magic;                             ///< 魔数标识
} mcu_buffer_block_footer_t;

/**
 * @brief 内存池信息
 */
typedef struct mcu_buffer_pool {
    uint32_t block_size;                        ///< 块大小
    uint32_t block_count;                       ///< 总块数
    uint32_t free_count;                        ///< 空闲块数
    uint32_t alloc_count;                       ///< 已分配块数
    uint32_t peak_usage;                        ///< 峰值使用量
    
    mcu_buffer_block_header_t *free_list;       ///< 空闲链表
    mcu_buffer_block_header_t *alloc_list;      ///< 已分配链表
    
    uint8_t *pool_memory;                       ///< 池内存区域
    uint32_t pool_size;                         ///< 池总大小
    
    rt_mutex_t mutex;                           ///< 池互斥锁
    
    /* 统计信息 */
    uint32_t total_alloc_count;                 ///< 总分配次数
    uint32_t total_free_count;                  ///< 总释放次数
    uint32_t alloc_failure_count;               ///< 分配失败次数
    uint32_t guard_violation_count;             ///< 保护字节违规次数
    
} mcu_buffer_pool_t;

/**
 * @brief 缓冲区管理器上下文
 */
typedef struct mcu_buffer_manager_context {
    mcu_uart_handle_base_t base;                ///< 基础句柄
    
    /* 内存池 */
    mcu_buffer_pool_t pools[3];                 ///< 三种大小的内存池
    uint32_t pool_count;                        ///< 池数量
    
    /* 配置参数 */
    mcu_transport_config_t config;              ///< 传输配置
    
    /* 全局统计 */
    mcu_buffer_stats_t stats;                   ///< 缓冲区统计
    
    /* 内存碎片整理 */
    rt_timer_t defrag_timer;                    ///< 碎片整理定时器
    bool defrag_enabled;                        ///< 碎片整理使能
    
} mcu_buffer_manager_context_t;

/* ================================ 静态函数声明 ================================ */

static mcu_uart_result_e mcu_buffer_pool_init(mcu_buffer_pool_t *pool,
                                              uint32_t block_size,
                                              uint32_t block_count,
                                              uint32_t pool_id);
static void mcu_buffer_pool_deinit(mcu_buffer_pool_t *pool);
static void *mcu_buffer_pool_alloc(mcu_buffer_pool_t *pool);
static mcu_uart_result_e mcu_buffer_pool_free(mcu_buffer_pool_t *pool, void *ptr);
static mcu_uart_result_e mcu_buffer_validate_block(const void *ptr);
static void mcu_buffer_set_guard_pattern(mcu_buffer_block_header_t *header);
static bool mcu_buffer_check_guard_pattern(const mcu_buffer_block_header_t *header);
static void mcu_buffer_defrag_timer_callback(void *parameter);
static mcu_uart_result_e mcu_buffer_pool_defragment(mcu_buffer_pool_t *pool);

/* ================================ 公共函数实现 ================================ */

/**
 * @brief 初始化缓冲区管理器
 * 
 * @param handle 管理器句柄输出
 * @param config 配置参数
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_buffer_manager_init(mcu_buffer_manager_handle_t *handle,
                                         const mcu_transport_config_t *config)
{
    mcu_uart_result_e result = MCU_UART_OK;
    mcu_buffer_manager_context_t *ctx = NULL;
    
    /* 检查输入参数 */
    if (handle == NULL || config == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 分配上下文内存 */
    ctx = (mcu_buffer_manager_context_t *)rt_malloc(sizeof(mcu_buffer_manager_context_t));
    if (ctx == NULL) {
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    /* 初始化上下文 */
    memset(ctx, 0, sizeof(mcu_buffer_manager_context_t));
    memcpy(&ctx->config, config, sizeof(mcu_transport_config_t));
    ctx->pool_count = 3;
    
    /* 初始化基础句柄 */
    result = mcu_uart_handle_base_init(&ctx->base, MCU_UART_MODULE_TRANSPORT, ctx);
    if (result != MCU_UART_OK) {
        rt_free(ctx);
        return result;
    }
    
    /* 初始化小块内存池 */
    result = mcu_buffer_pool_init(&ctx->pools[0], 
                                 MCU_BUFFER_SMALL_BLOCK_SIZE,
                                 MCU_BUFFER_SMALL_POOL_COUNT,
                                 0);
    if (result != MCU_UART_OK) {
        mcu_uart_handle_base_deinit(&ctx->base);
        rt_free(ctx);
        return result;
    }
    
    /* 初始化中等大小内存池 */
    result = mcu_buffer_pool_init(&ctx->pools[1],
                                 MCU_BUFFER_MEDIUM_BLOCK_SIZE,
                                 MCU_BUFFER_MEDIUM_POOL_COUNT,
                                 1);
    if (result != MCU_UART_OK) {
        mcu_buffer_pool_deinit(&ctx->pools[0]);
        mcu_uart_handle_base_deinit(&ctx->base);
        rt_free(ctx);
        return result;
    }
    
    /* 初始化大块内存池 */
    result = mcu_buffer_pool_init(&ctx->pools[2],
                                 MCU_BUFFER_LARGE_BLOCK_SIZE,
                                 MCU_BUFFER_LARGE_POOL_COUNT,
                                 2);
    if (result != MCU_UART_OK) {
        mcu_buffer_pool_deinit(&ctx->pools[1]);
        mcu_buffer_pool_deinit(&ctx->pools[0]);
        mcu_uart_handle_base_deinit(&ctx->base);
        rt_free(ctx);
        return result;
    }
    
    /* 创建碎片整理定时器 */
    if (config->enable_defragmentation) {
        ctx->defrag_timer = rt_timer_create("buf_defrag",
                                           mcu_buffer_defrag_timer_callback,
                                           ctx,
                                           rt_tick_from_millisecond(config->defrag_interval_ms),
                                           RT_TIMER_FLAG_PERIODIC);
        if (ctx->defrag_timer == NULL) {
            mcu_buffer_pool_deinit(&ctx->pools[2]);
            mcu_buffer_pool_deinit(&ctx->pools[1]);
            mcu_buffer_pool_deinit(&ctx->pools[0]);
            mcu_uart_handle_base_deinit(&ctx->base);
            rt_free(ctx);
            return MCU_UART_ERROR_NO_MEMORY;
        }
        
        ctx->defrag_enabled = true;
        rt_timer_start(ctx->defrag_timer);
    }
    
    /* 设置输出句柄 */
    *handle = (mcu_buffer_manager_handle_t)ctx;
    
    return MCU_UART_OK;
}

/**
 * @brief 反初始化缓冲区管理器
 * 
 * @param handle 管理器句柄
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_buffer_manager_deinit(mcu_buffer_manager_handle_t handle)
{
    mcu_buffer_manager_context_t *ctx = (mcu_buffer_manager_context_t *)handle;
    uint32_t i;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_TRANSPORT);
    
    /* 停止并删除碎片整理定时器 */
    if (ctx->defrag_timer) {
        rt_timer_stop(ctx->defrag_timer);
        rt_timer_delete(ctx->defrag_timer);
    }
    
    /* 反初始化所有内存池 */
    for (i = 0; i < ctx->pool_count; i++) {
        mcu_buffer_pool_deinit(&ctx->pools[i]);
    }
    
    /* 反初始化基础句柄 */
    mcu_uart_handle_base_deinit(&ctx->base);
    
    /* 释放内存 */
    rt_free(ctx);
    
    return MCU_UART_OK;
}

/**
 * @brief 分配缓冲区
 * 
 * @param handle 管理器句柄
 * @param size 请求大小
 * @return void* 缓冲区指针，失败返回NULL
 */
void *mcu_buffer_manager_alloc(mcu_buffer_manager_handle_t handle, uint32_t size)
{
    mcu_buffer_manager_context_t *ctx = (mcu_buffer_manager_context_t *)handle;
    void *ptr = NULL;
    uint32_t i;
    
    /* 验证句柄 */
    if (mcu_uart_validate_handle(&ctx->base, MCU_UART_MODULE_TRANSPORT) != MCU_UART_OK) {
        return NULL;
    }
    
    if (size == 0) {
        return NULL;
    }
    
    MCU_UART_LOCK_MODULE(ctx);
    
    /* 选择合适的内存池 */
    for (i = 0; i < ctx->pool_count; i++) {
        if (size <= ctx->pools[i].block_size - sizeof(mcu_buffer_block_header_t) - sizeof(mcu_buffer_block_footer_t)) {
            ptr = mcu_buffer_pool_alloc(&ctx->pools[i]);
            if (ptr != NULL) {
                ctx->stats.total_alloc_count++;
                ctx->stats.current_usage += ctx->pools[i].block_size;
                if (ctx->stats.current_usage > ctx->stats.peak_usage) {
                    ctx->stats.peak_usage = ctx->stats.current_usage;
                }
                break;
            }
        }
    }
    
    if (ptr == NULL) {
        ctx->stats.alloc_failure_count++;
    }
    
    MCU_UART_UNLOCK_MODULE(ctx);
    
    return ptr;
}

/**
 * @brief 释放缓冲区
 * 
 * @param handle 管理器句柄
 * @param ptr 缓冲区指针
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_buffer_manager_free(mcu_buffer_manager_handle_t handle, void *ptr)
{
    mcu_buffer_manager_context_t *ctx = (mcu_buffer_manager_context_t *)handle;
    mcu_uart_result_e result = MCU_UART_ERROR_INVALID_PARAM;
    mcu_buffer_block_header_t *header;
    uint32_t pool_id;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_TRANSPORT);
    
    if (ptr == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 验证缓冲区块 */
    result = mcu_buffer_validate_block(ptr);
    if (result != MCU_UART_OK) {
        return result;
    }
    
    /* 获取块头部信息 */
    header = (mcu_buffer_block_header_t *)((uint8_t *)ptr - sizeof(mcu_buffer_block_header_t));
    pool_id = header->pool_id;
    
    MCU_UART_LOCK_MODULE(ctx);
    
    /* 释放到对应的内存池 */
    if (pool_id < ctx->pool_count) {
        result = mcu_buffer_pool_free(&ctx->pools[pool_id], ptr);
        if (result == MCU_UART_OK) {
            ctx->stats.total_free_count++;
            ctx->stats.current_usage -= ctx->pools[pool_id].block_size;
        }
    } else {
        result = MCU_UART_ERROR_INVALID_PARAM;
    }
    
    MCU_UART_UNLOCK_MODULE(ctx);
    
    return result;
}

/**
 * @brief 获取缓冲区信息
 * 
 * @param handle 管理器句柄
 * @param ptr 缓冲区指针
 * @param info 信息输出
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_buffer_manager_get_info(mcu_buffer_manager_handle_t handle,
                                             const void *ptr,
                                             mcu_buffer_info_t *info)
{
    mcu_buffer_manager_context_t *ctx = (mcu_buffer_manager_context_t *)handle;
    mcu_buffer_block_header_t *header;
    mcu_uart_result_e result;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_TRANSPORT);
    
    if (ptr == NULL || info == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 验证缓冲区块 */
    result = mcu_buffer_validate_block(ptr);
    if (result != MCU_UART_OK) {
        return result;
    }
    
    /* 获取块信息 */
    header = (mcu_buffer_block_header_t *)((const uint8_t *)ptr - sizeof(mcu_buffer_block_header_t));
    
    MCU_UART_LOCK_MODULE(ctx);
    
    info->size = header->size - sizeof(mcu_buffer_block_header_t) - sizeof(mcu_buffer_block_footer_t);
    info->pool_id = header->pool_id;
    info->alloc_time = header->alloc_time;
    info->age_ticks = rt_tick_get() - header->alloc_time;
    
    MCU_UART_UNLOCK_MODULE(ctx);
    
    return MCU_UART_OK;
}

/**
 * @brief 获取缓冲区统计信息
 * 
 * @param handle 管理器句柄
 * @param stats 统计信息输出
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_buffer_manager_get_stats(mcu_buffer_manager_handle_t handle,
                                              mcu_buffer_stats_t *stats)
{
    mcu_buffer_manager_context_t *ctx = (mcu_buffer_manager_context_t *)handle;
    uint32_t i;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_TRANSPORT);
    
    if (stats == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    MCU_UART_LOCK_MODULE(ctx);
    
    /* 复制基础统计信息 */
    memcpy(stats, &ctx->stats, sizeof(mcu_buffer_stats_t));
    
    /* 计算池统计信息 */
    stats->total_pool_size = 0;
    stats->total_free_size = 0;
    stats->fragmentation_ratio = 0;
    
    for (i = 0; i < ctx->pool_count; i++) {
        mcu_buffer_pool_t *pool = &ctx->pools[i];
        
        stats->total_pool_size += pool->pool_size;
        stats->total_free_size += pool->free_count * pool->block_size;
        
        stats->pool_stats[i].block_size = pool->block_size;
        stats->pool_stats[i].total_blocks = pool->block_count;
        stats->pool_stats[i].free_blocks = pool->free_count;
        stats->pool_stats[i].alloc_blocks = pool->alloc_count;
        stats->pool_stats[i].peak_usage = pool->peak_usage;
        stats->pool_stats[i].total_allocs = pool->total_alloc_count;
        stats->pool_stats[i].total_frees = pool->total_free_count;
        stats->pool_stats[i].alloc_failures = pool->alloc_failure_count;
    }
    
    /* 计算碎片化率 */
    if (stats->total_pool_size > 0) {
        stats->fragmentation_ratio = (stats->total_pool_size - stats->total_free_size) * 100 / stats->total_pool_size;
    }
    
    MCU_UART_UNLOCK_MODULE(ctx);
    
    return MCU_UART_OK;
}

/**
 * @brief 手动触发碎片整理
 * 
 * @param handle 管理器句柄
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_buffer_manager_defragment(mcu_buffer_manager_handle_t handle)
{
    mcu_buffer_manager_context_t *ctx = (mcu_buffer_manager_context_t *)handle;
    mcu_uart_result_e result = MCU_UART_OK;
    uint32_t i;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_TRANSPORT);
    
    MCU_UART_LOCK_MODULE(ctx);
    
    /* 对所有内存池进行碎片整理 */
    for (i = 0; i < ctx->pool_count; i++) {
        mcu_uart_result_e pool_result = mcu_buffer_pool_defragment(&ctx->pools[i]);
        if (pool_result != MCU_UART_OK) {
            result = pool_result;
        }
    }
    
    MCU_UART_UNLOCK_MODULE(ctx);
    
    return result;
}

/* ================================ 静态函数实现 ================================ */

/**
 * @brief 初始化内存池
 */
static mcu_uart_result_e mcu_buffer_pool_init(mcu_buffer_pool_t *pool,
                                              uint32_t block_size,
                                              uint32_t block_count,
                                              uint32_t pool_id)
{
    uint32_t i;
    uint8_t *memory;
    mcu_buffer_block_header_t *header;
    char mutex_name[32];
    
    memset(pool, 0, sizeof(mcu_buffer_pool_t));
    
    pool->block_size = block_size;
    pool->block_count = block_count;
    pool->free_count = block_count;
    pool->pool_size = block_count * block_size;
    
    /* 分配内存池 */
    pool->pool_memory = (uint8_t *)rt_malloc(pool->pool_size);
    if (pool->pool_memory == NULL) {
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    /* 创建互斥锁 */
    snprintf(mutex_name, sizeof(mutex_name), "pool_%lu", pool_id);
    pool->mutex = rt_mutex_create(mutex_name, RT_IPC_FLAG_FIFO);
    if (pool->mutex == NULL) {
        rt_free(pool->pool_memory);
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    /* 初始化空闲链表 */
    memory = pool->pool_memory;
    for (i = 0; i < block_count; i++) {
        header = (mcu_buffer_block_header_t *)memory;
        header->magic = MCU_BUFFER_MAGIC_FREE;
        header->size = block_size;
        header->pool_id = pool_id;
        header->alloc_time = 0;
        header->next = (i == block_count - 1) ? NULL : 
                      (mcu_buffer_block_header_t *)(memory + block_size);
        mcu_buffer_set_guard_pattern(header);
        
        memory += block_size;
    }
    
    pool->free_list = (mcu_buffer_block_header_t *)pool->pool_memory;
    
    return MCU_UART_OK;
}

/**
 * @brief 反初始化内存池
 */
static void mcu_buffer_pool_deinit(mcu_buffer_pool_t *pool)
{
    if (pool == NULL) return;
    
    if (pool->mutex) {
        rt_mutex_delete(pool->mutex);
    }
    
    if (pool->pool_memory) {
        rt_free(pool->pool_memory);
    }
    
    memset(pool, 0, sizeof(mcu_buffer_pool_t));
}

/**
 * @brief 从内存池分配块
 */
static void *mcu_buffer_pool_alloc(mcu_buffer_pool_t *pool)
{
    mcu_buffer_block_header_t *header;
    void *user_ptr = NULL;
    
    rt_mutex_take(pool->mutex, RT_WAITING_FOREVER);
    
    if (pool->free_list != NULL) {
        /* 从空闲链表取出第一个块 */
        header = pool->free_list;
        pool->free_list = header->next;
        
        /* 更新统计 */
        pool->free_count--;
        pool->alloc_count++;
        pool->total_alloc_count++;
        
        if (pool->alloc_count > pool->peak_usage) {
            pool->peak_usage = pool->alloc_count;
        }
        
        /* 设置块信息 */
        header->magic = MCU_BUFFER_MAGIC_ALLOCATED;
        header->alloc_time = rt_tick_get();
        header->next = pool->alloc_list;
        pool->alloc_list = header;
        
        /* 设置保护模式 */
        mcu_buffer_set_guard_pattern(header);
        
        /* 返回用户可用地址 */
        user_ptr = (uint8_t *)header + sizeof(mcu_buffer_block_header_t);
    } else {
        pool->alloc_failure_count++;
    }
    
    rt_mutex_release(pool->mutex);
    
    return user_ptr;
}

/**
 * @brief 释放块到内存池
 */
static mcu_uart_result_e mcu_buffer_pool_free(mcu_buffer_pool_t *pool, void *ptr)
{
    mcu_buffer_block_header_t *header, *prev;
    mcu_uart_result_e result = MCU_UART_OK;
    
    if (ptr == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    header = (mcu_buffer_block_header_t *)((uint8_t *)ptr - sizeof(mcu_buffer_block_header_t));
    
    rt_mutex_take(pool->mutex, RT_WAITING_FOREVER);
    
    /* 检查保护模式 */
    if (!mcu_buffer_check_guard_pattern(header)) {
        pool->guard_violation_count++;
        result = MCU_UART_ERROR_BUFFER_CORRUPTION;
        goto exit;
    }
    
    /* 从已分配链表中移除 */
    if (pool->alloc_list == header) {
        pool->alloc_list = header->next;
    } else {
        prev = pool->alloc_list;
        while (prev != NULL && prev->next != header) {
            prev = prev->next;
        }
        if (prev != NULL) {
            prev->next = header->next;
        }
    }
    
    /* 添加到空闲链表 */
    header->magic = MCU_BUFFER_MAGIC_FREE;
    header->alloc_time = 0;
    header->next = pool->free_list;
    pool->free_list = header;
    
    /* 更新统计 */
    pool->alloc_count--;
    pool->free_count++;
    pool->total_free_count++;
    
exit:
    rt_mutex_release(pool->mutex);
    return result;
}

/**
 * @brief 验证缓冲区块
 */
static mcu_uart_result_e mcu_buffer_validate_block(const void *ptr)
{
    const mcu_buffer_block_header_t *header;
    const mcu_buffer_block_footer_t *footer;
    
    if (ptr == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    header = (const mcu_buffer_block_header_t *)((const uint8_t *)ptr - sizeof(mcu_buffer_block_header_t));
    
    /* 检查魔数 */
    if (header->magic != MCU_BUFFER_MAGIC_ALLOCATED) {
        return MCU_UART_ERROR_BUFFER_CORRUPTION;
    }
    
    /* 检查保护字节 */
    if (!mcu_buffer_check_guard_pattern(header)) {
        return MCU_UART_ERROR_BUFFER_CORRUPTION;
    }
    
    return MCU_UART_OK;
}

/**
 * @brief 设置保护模式
 */
static void mcu_buffer_set_guard_pattern(mcu_buffer_block_header_t *header)
{
    mcu_buffer_block_footer_t *footer;
    
    header->guard_start = MCU_BUFFER_GUARD_PATTERN;
    
    footer = (mcu_buffer_block_footer_t *)((uint8_t *)header + header->size - sizeof(mcu_buffer_block_footer_t));
    footer->guard_end = MCU_BUFFER_GUARD_PATTERN;
    footer->magic = header->magic;
}

/**
 * @brief 检查保护模式
 */
static bool mcu_buffer_check_guard_pattern(const mcu_buffer_block_header_t *header)
{
    const mcu_buffer_block_footer_t *footer;
    
    if (header->guard_start != MCU_BUFFER_GUARD_PATTERN) {
        return false;
    }
    
    footer = (const mcu_buffer_block_footer_t *)((const uint8_t *)header + header->size - sizeof(mcu_buffer_block_footer_t));
    
    if (footer->guard_end != MCU_BUFFER_GUARD_PATTERN || footer->magic != header->magic) {
        return false;
    }
    
    return true;
}

/**
 * @brief 碎片整理定时器回调
 */
static void mcu_buffer_defrag_timer_callback(void *parameter)
{
    mcu_buffer_manager_context_t *ctx = (mcu_buffer_manager_context_t *)parameter;
    
    if (ctx != NULL && ctx->defrag_enabled) {
        mcu_buffer_manager_defragment((mcu_buffer_manager_handle_t)ctx);
    }
}

/**
 * @brief 内存池碎片整理
 */
static mcu_uart_result_e mcu_buffer_pool_defragment(mcu_buffer_pool_t *pool)
{
    /* 当前实现为简单的空闲块重新排列 */
    /* 在实际应用中可以实现更复杂的碎片整理算法 */
    
    rt_mutex_take(pool->mutex, RT_WAITING_FOREVER);
    
    /* 重新构建空闲链表，按地址顺序排列 */
    mcu_buffer_block_header_t *sorted_list = NULL;
    mcu_buffer_block_header_t *current = pool->free_list;
    
    while (current != NULL) {
        mcu_buffer_block_header_t *next = current->next;
        
        /* 插入排序 */
        if (sorted_list == NULL || current < sorted_list) {
            current->next = sorted_list;
            sorted_list = current;
        } else {
            mcu_buffer_block_header_t *pos = sorted_list;
            while (pos->next != NULL && pos->next < current) {
                pos = pos->next;
            }
            current->next = pos->next;
            pos->next = current;
        }
        
        current = next;
    }
    
    pool->free_list = sorted_list;
    
    rt_mutex_release(pool->mutex);
    
    return MCU_UART_OK;
} 