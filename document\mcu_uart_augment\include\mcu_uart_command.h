/**
 * @file mcu_uart_command.h
 * @brief MCU UART增强版命令处理模块
 * @details 完整的命令分发、队列管理和处理系统
 * @version 1.0.0
 * @date 2025-01-19
 */

#ifndef MCU_UART_COMMAND_H
#define MCU_UART_COMMAND_H

#ifdef __cplusplus
extern "C" {
#endif

#include "mcu_uart_types.h"
#include "mcu_uart_config.h"

/* ========== 命令相关常量 ========== */

/** @brief 命令优先级定义 */
typedef enum mcu_uart_command_priority_e {
    MCU_UART_CMD_PRIORITY_LOW = 0,         /**< 低优先级 */
    MCU_UART_CMD_PRIORITY_NORMAL,          /**< 普通优先级 */
    MCU_UART_CMD_PRIORITY_HIGH,            /**< 高优先级 */
    MCU_UART_CMD_PRIORITY_URGENT,          /**< 紧急优先级 */
    MCU_UART_CMD_PRIORITY_MAX
} mcu_uart_command_priority_t;

/** @brief 命令状态定义 */
typedef enum mcu_uart_command_state_e {
    MCU_UART_CMD_STATE_PENDING = 0,        /**< 待处理 */
    MCU_UART_CMD_STATE_PROCESSING,         /**< 处理中 */
    MCU_UART_CMD_STATE_COMPLETED,          /**< 已完成 */
    MCU_UART_CMD_STATE_FAILED,             /**< 失败 */
    MCU_UART_CMD_STATE_TIMEOUT,            /**< 超时 */
    MCU_UART_CMD_STATE_CANCELLED,          /**< 已取消 */
    MCU_UART_CMD_STATE_MAX
} mcu_uart_command_state_t;

/** @brief 命令类型定义 */
typedef enum mcu_uart_command_type_e {
    MCU_UART_CMD_TYPE_SYSTEM = 0,          /**< 系统命令 */
    MCU_UART_CMD_TYPE_TUNER,               /**< 调谐器命令 */
    MCU_UART_CMD_TYPE_USB,                 /**< USB命令 */
    MCU_UART_CMD_TYPE_BT,                  /**< 蓝牙命令 */
    MCU_UART_CMD_TYPE_SXM,                 /**< SXM命令 */
    MCU_UART_CMD_TYPE_PANDORA,             /**< Pandora命令 */
    MCU_UART_CMD_TYPE_UPGRADE,             /**< 升级命令 */
    MCU_UART_CMD_TYPE_BYPASS,              /**< 透传命令 */
    MCU_UART_CMD_TYPE_CUSTOM,              /**< 自定义命令 */
    MCU_UART_CMD_TYPE_MAX
} mcu_uart_command_type_t;

/* ========== 命令结构定义 ========== */

/**
 * @brief 命令结构
 */
typedef struct mcu_uart_command_s {
    __u16 command_id;                      /**< 命令ID */
    mcu_uart_command_type_t type;          /**< 命令类型 */
    mcu_uart_command_priority_t priority;  /**< 优先级 */
    mcu_uart_command_state_t state;        /**< 命令状态 */
    
    /* 命令数据 */
    __u8 *data;                            /**< 命令数据 */
    __u32 data_length;                     /**< 数据长度 */
    __u32 max_data_length;                 /**< 最大数据长度 */
    
    /* 目标信息 */
    mcu_uart_device_addr_t target_addr;    /**< 目标设备地址 */
    __u8 zone_mask;                        /**< 区域掩码 */
    bool ack_required;                     /**< 是否需要ACK */
    
    /* 时间信息 */
    __u32 created_time;                    /**< 创建时间 */
    __u32 start_time;                      /**< 开始处理时间 */
    __u32 complete_time;                   /**< 完成时间 */
    __u32 timeout_ms;                      /**< 超时时间 */
    
    /* 重试信息 */
    __u32 retry_count;                     /**< 重试次数 */
    __u32 max_retry_count;                 /**< 最大重试次数 */
    
    /* 回调和上下文 */
    mcu_uart_command_handler_f handler;    /**< 命令处理器 */
    void *context;                         /**< 用户上下文 */
    
    /* 链表节点 */
    struct mcu_uart_command_s *next;       /**< 下一个命令 */
    struct mcu_uart_command_s *prev;       /**< 上一个命令 */
    
    /* 内部状态 */
    __u32 magic;                           /**< 魔数验证 */
} mcu_uart_command_t;

/**
 * @brief 命令队列结构
 */
typedef struct mcu_uart_command_queue_s {
    mcu_uart_command_t *head;              /**< 队列头 */
    mcu_uart_command_t *tail;              /**< 队列尾 */
    __u32 count;                           /**< 命令数量 */
    __u32 max_count;                       /**< 最大命令数量 */
    
    /* 优先级队列 */
    mcu_uart_command_t *priority_heads[MCU_UART_CMD_PRIORITY_MAX]; /**< 各优先级队列头 */
    __u32 priority_counts[MCU_UART_CMD_PRIORITY_MAX];              /**< 各优先级命令数 */
    
    /* 统计信息 */
    __u32 total_enqueued;                  /**< 总入队数 */
    __u32 total_dequeued;                  /**< 总出队数 */
    __u32 total_processed;                 /**< 总处理数 */
    __u32 total_failed;                    /**< 总失败数 */
    __u32 total_timeout;                   /**< 总超时数 */
    __u32 max_queue_size;                  /**< 历史最大队列大小 */
    __u32 overflow_count;                  /**< 溢出次数 */
    
    /* 配置参数 */
    bool priority_enabled;                 /**< 是否启用优先级 */
    bool timeout_enabled;                  /**< 是否启用超时 */
    __u32 default_timeout_ms;              /**< 默认超时时间 */
    
    /* 同步和保护 */
    void *lock;                            /**< 锁对象 */
    __u32 magic;                           /**< 魔数验证 */
} mcu_uart_command_queue_t;

/**
 * @brief 命令处理器注册结构
 */
typedef struct mcu_uart_command_handler_entry_s {
    __u16 command_start;                   /**< 命令范围开始 */
    __u16 command_end;                     /**< 命令范围结束 */
    mcu_uart_command_type_t type;          /**< 命令类型 */
    mcu_uart_command_handler_f handler;    /**< 处理函数 */
    void *context;                         /**< 处理上下文 */
    bool is_active;                        /**< 是否激活 */
    __u32 call_count;                      /**< 调用次数 */
    __u32 success_count;                   /**< 成功次数 */
    __u32 failure_count;                   /**< 失败次数 */
    struct mcu_uart_command_handler_entry_s *next; /**< 下一个处理器 */
} mcu_uart_command_handler_entry_t;

/**
 * @brief 命令分发器结构
 */
typedef struct mcu_uart_command_dispatcher_s {
    mcu_uart_command_handler_entry_t *handlers; /**< 处理器链表 */
    __u32 handler_count;                   /**< 处理器数量 */
    __u32 max_handlers;                    /**< 最大处理器数量 */
    
    /* 默认处理器 */
    mcu_uart_command_handler_f default_handler; /**< 默认处理器 */
    void *default_context;                 /**< 默认上下文 */
    
    /* 统计信息 */
    __u32 total_dispatched;                /**< 总分发数 */
    __u32 successful_dispatches;           /**< 成功分发数 */
    __u32 failed_dispatches;               /**< 失败分发数 */
    __u32 unhandled_commands;              /**< 未处理命令数 */
    
    /* 同步和保护 */
    void *lock;                            /**< 锁对象 */
    __u32 magic;                           /**< 魔数验证 */
} mcu_uart_command_dispatcher_t;

/**
 * @brief 命令处理器统计信息
 */
typedef struct mcu_uart_command_stats_s {
    __u32 total_commands;                  /**< 总命令数 */
    __u32 pending_commands;                /**< 待处理命令数 */
    __u32 processing_commands;             /**< 处理中命令数 */
    __u32 completed_commands;              /**< 已完成命令数 */
    __u32 failed_commands;                 /**< 失败命令数 */
    __u32 timeout_commands;                /**< 超时命令数 */
    __u32 cancelled_commands;              /**< 取消命令数 */
    __u32 average_processing_time_ms;      /**< 平均处理时间 */
    __u32 max_processing_time_ms;          /**< 最大处理时间 */
    __u32 min_processing_time_ms;          /**< 最小处理时间 */
    __u32 queue_utilization_percent;       /**< 队列利用率 */
    __u32 handler_efficiency_percent;      /**< 处理器效率 */
} mcu_uart_command_stats_t;

/* ========== 命令队列API ========== */

/**
 * @brief 初始化命令队列
 * @param[out] queue 队列指针
 * @param[in] max_count 最大命令数量
 * @param[in] enable_priority 是否启用优先级
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_queue_init(
    mcu_uart_command_queue_t *queue,
    __u32 max_count,
    bool enable_priority
);

/**
 * @brief 销毁命令队列
 * @param[in] queue 队列指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_queue_deinit(mcu_uart_command_queue_t *queue);

/**
 * @brief 向队列添加命令
 * @param[in] queue 队列指针
 * @param[in] command 命令结构
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_queue_enqueue(
    mcu_uart_command_queue_t *queue,
    mcu_uart_command_t *command
);

/**
 * @brief 从队列获取命令
 * @param[in] queue 队列指针
 * @param[out] command 命令结构指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_queue_dequeue(
    mcu_uart_command_queue_t *queue,
    mcu_uart_command_t **command
);

/**
 * @brief 窥视队列中的命令(不移除)
 * @param[in] queue 队列指针
 * @param[out] command 命令结构指针
 * @param[in] index 索引(0为最前面)
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_queue_peek(
    mcu_uart_command_queue_t *queue,
    mcu_uart_command_t **command,
    __u32 index
);

/**
 * @brief 从队列中移除指定命令
 * @param[in] queue 队列指针
 * @param[in] command 要移除的命令
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_queue_remove(
    mcu_uart_command_queue_t *queue,
    mcu_uart_command_t *command
);

/**
 * @brief 清空命令队列
 * @param[in] queue 队列指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_queue_clear(mcu_uart_command_queue_t *queue);

/* ========== 命令分发器API ========== */

/**
 * @brief 初始化命令分发器
 * @param[out] dispatcher 分发器指针
 * @param[in] max_handlers 最大处理器数量
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_dispatcher_init(
    mcu_uart_command_dispatcher_t *dispatcher,
    __u32 max_handlers
);

/**
 * @brief 销毁命令分发器
 * @param[in] dispatcher 分发器指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_dispatcher_deinit(mcu_uart_command_dispatcher_t *dispatcher);

/**
 * @brief 注册命令处理器
 * @param[in] dispatcher 分发器指针
 * @param[in] command_start 命令范围开始
 * @param[in] command_end 命令范围结束
 * @param[in] type 命令类型
 * @param[in] handler 处理函数
 * @param[in] context 处理上下文
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_dispatcher_register(
    mcu_uart_command_dispatcher_t *dispatcher,
    __u16 command_start,
    __u16 command_end,
    mcu_uart_command_type_t type,
    mcu_uart_command_handler_f handler,
    void *context
);

/**
 * @brief 注销命令处理器
 * @param[in] dispatcher 分发器指针
 * @param[in] command_start 命令范围开始
 * @param[in] command_end 命令范围结束
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_dispatcher_unregister(
    mcu_uart_command_dispatcher_t *dispatcher,
    __u16 command_start,
    __u16 command_end
);

/**
 * @brief 分发命令
 * @param[in] dispatcher 分发器指针
 * @param[in] command 命令结构
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_dispatcher_dispatch(
    mcu_uart_command_dispatcher_t *dispatcher,
    mcu_uart_command_t *command
);

/**
 * @brief 设置默认处理器
 * @param[in] dispatcher 分发器指针
 * @param[in] handler 默认处理函数
 * @param[in] context 处理上下文
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_dispatcher_set_default(
    mcu_uart_command_dispatcher_t *dispatcher,
    mcu_uart_command_handler_f handler,
    void *context
);

/* ========== 命令管理API ========== */

/**
 * @brief 创建命令
 * @param[in] command_id 命令ID
 * @param[in] type 命令类型
 * @param[in] priority 优先级
 * @param[in] data 命令数据
 * @param[in] data_length 数据长度
 * @return 命令指针，失败返回NULL
 */
mcu_uart_command_t* mcu_uart_command_create(
    __u16 command_id,
    mcu_uart_command_type_t type,
    mcu_uart_command_priority_t priority,
    const __u8 *data,
    __u32 data_length
);

/**
 * @brief 销毁命令
 * @param[in] command 命令指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_destroy(mcu_uart_command_t *command);

/**
 * @brief 复制命令
 * @param[in] src 源命令
 * @param[out] dst 目标命令
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_copy(
    const mcu_uart_command_t *src,
    mcu_uart_command_t *dst
);

/**
 * @brief 设置命令超时
 * @param[in] command 命令指针
 * @param[in] timeout_ms 超时时间
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_set_timeout(
    mcu_uart_command_t *command,
    __u32 timeout_ms
);

/**
 * @brief 设置命令目标
 * @param[in] command 命令指针
 * @param[in] target_addr 目标地址
 * @param[in] zone_mask 区域掩码
 * @param[in] ack_required 是否需要ACK
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_set_target(
    mcu_uart_command_t *command,
    mcu_uart_device_addr_t target_addr,
    __u8 zone_mask,
    bool ack_required
);

/**
 * @brief 检查命令是否超时
 * @param[in] command 命令指针
 * @return true=超时, false=未超时
 */
bool mcu_uart_command_is_timeout(const mcu_uart_command_t *command);

/* ========== 状态查询API ========== */

/**
 * @brief 获取队列中命令数量
 * @param[in] queue 队列指针
 * @return 命令数量
 */
static inline __u32 mcu_uart_command_queue_count(const mcu_uart_command_queue_t *queue)
{
    return (queue) ? queue->count : 0;
}

/**
 * @brief 检查队列是否为空
 * @param[in] queue 队列指针
 * @return true=空, false=非空
 */
static inline bool mcu_uart_command_queue_is_empty(const mcu_uart_command_queue_t *queue)
{
    return (queue) ? (queue->count == 0) : true;
}

/**
 * @brief 检查队列是否已满
 * @param[in] queue 队列指针
 * @return true=满, false=未满
 */
static inline bool mcu_uart_command_queue_is_full(const mcu_uart_command_queue_t *queue)
{
    return (queue) ? (queue->count >= queue->max_count) : false;
}

/* ========== 统计和调试API ========== */

/**
 * @brief 获取命令统计信息
 * @param[in] queue 队列指针
 * @param[in] dispatcher 分发器指针
 * @param[out] stats 统计信息结构
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_get_stats(
    const mcu_uart_command_queue_t *queue,
    const mcu_uart_command_dispatcher_t *dispatcher,
    mcu_uart_command_stats_t *stats
);

/**
 * @brief 重置命令统计信息
 * @param[in] queue 队列指针
 * @param[in] dispatcher 分发器指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_command_reset_stats(
    mcu_uart_command_queue_t *queue,
    mcu_uart_command_dispatcher_t *dispatcher
);

/**
 * @brief 打印命令队列状态
 * @param[in] queue 队列指针
 */
void mcu_uart_command_queue_print_status(const mcu_uart_command_queue_t *queue);

/**
 * @brief 打印命令分发器状态
 * @param[in] dispatcher 分发器指针
 */
void mcu_uart_command_dispatcher_print_status(const mcu_uart_command_dispatcher_t *dispatcher);

/**
 * @brief 打印命令信息
 * @param[in] command 命令指针
 */
void mcu_uart_command_print_info(const mcu_uart_command_t *command);

/* ========== 工具函数 ========== */

/**
 * @brief 获取命令类型字符串
 * @param[in] type 命令类型
 * @return 类型字符串
 */
const char* mcu_uart_command_get_type_string(mcu_uart_command_type_t type);

/**
 * @brief 获取命令状态字符串
 * @param[in] state 命令状态
 * @return 状态字符串
 */
const char* mcu_uart_command_get_state_string(mcu_uart_command_state_t state);

/**
 * @brief 获取命令优先级字符串
 * @param[in] priority 命令优先级
 * @return 优先级字符串
 */
const char* mcu_uart_command_get_priority_string(mcu_uart_command_priority_t priority);

/**
 * @brief 根据命令ID获取命令类型
 * @param[in] command_id 命令ID
 * @return 命令类型
 */
mcu_uart_command_type_t mcu_uart_command_get_type_by_id(__u16 command_id);

/* ========== 向前兼容性定义 ========== */

/** @brief 保持与原有结构的兼容性 */
typedef mcu_uart_command_t command_t;
typedef mcu_uart_command_queue_t command_queue_t;

/** @brief 保持与原有函数的兼容性 */
#define command_queue_init              mcu_uart_command_queue_init
#define command_queue_enqueue           mcu_uart_command_queue_enqueue
#define command_queue_dequeue           mcu_uart_command_queue_dequeue
#define command_create                  mcu_uart_command_create
#define command_destroy                 mcu_uart_command_destroy

#ifdef __cplusplus
}
#endif

#endif /* MCU_UART_COMMAND_H */
