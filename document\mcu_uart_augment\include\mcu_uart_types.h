/**
 * @file mcu_uart_types.h
 * @brief MCU UART增强版统一类型定义
 * @details 基于原始代码和重构版本的完整类型定义系统
 * @version 1.0.0
 * @date 2025-01-19
 * <AUTHOR> UART Augment Team
 */

#ifndef MCU_UART_TYPES_H
#define MCU_UART_TYPES_H

#ifdef __cplusplus
extern "C" {
#endif

/* ========== 平台相关包含 ========== */
#include <typedef.h>
#include <elibs_stdio.h>
#include <kapi.h>
#include <string.h>
#include <log.h>

/* ========== 基础类型定义 ========== */

/** @brief 布尔类型定义 */
#ifndef bool
typedef enum {
    false = 0,
    true = 1
} bool;
#endif

/** @brief 基础数据类型 */
typedef __u8  mcu_uart_byte_t;
typedef __u16 mcu_uart_word_t;
typedef __u32 mcu_uart_dword_t;
typedef __s32 mcu_uart_result_t;

/* ========== 错误码定义 ========== */

/** @brief MCU UART错误码枚举 */
typedef enum mcu_uart_error_e {
    MCU_UART_ERROR_NONE = 0,               /**< 操作成功 */
    MCU_UART_ERROR_INVALID_PARAM,          /**< 参数无效 */
    MCU_UART_ERROR_NULL_POINTER,           /**< 空指针错误 */
    MCU_UART_ERROR_BUFFER_FULL,            /**< 缓冲区满 */
    MCU_UART_ERROR_BUFFER_EMPTY,           /**< 缓冲区空 */
    MCU_UART_ERROR_BUFFER_OVERFLOW,        /**< 缓冲区溢出 */
    MCU_UART_ERROR_CHECKSUM_FAIL,          /**< 校验和失败 */
    MCU_UART_ERROR_CRC_FAIL,               /**< CRC校验失败 */
    MCU_UART_ERROR_TIMEOUT,                /**< 操作超时 */
    MCU_UART_ERROR_NO_MEMORY,              /**< 内存不足 */
    MCU_UART_ERROR_DEVICE_FAIL,            /**< 设备故障 */
    MCU_UART_ERROR_DEVICE_BUSY,            /**< 设备忙 */
    MCU_UART_ERROR_DEVICE_NOT_READY,       /**< 设备未就绪 */
    MCU_UART_ERROR_PROTOCOL_ERROR,         /**< 协议错误 */
    MCU_UART_ERROR_PACKET_TOO_LARGE,       /**< 数据包过大 */
    MCU_UART_ERROR_PACKET_INVALID,         /**< 数据包无效 */
    MCU_UART_ERROR_NOT_SUPPORTED,          /**< 不支持的操作 */
    MCU_UART_ERROR_NOT_INITIALIZED,        /**< 未初始化 */
    MCU_UART_ERROR_ALREADY_INITIALIZED,    /**< 已初始化 */
    MCU_UART_ERROR_RESOURCE_BUSY,          /**< 资源忙 */
    MCU_UART_ERROR_RESOURCE_NOT_AVAILABLE, /**< 资源不可用 */
    MCU_UART_ERROR_OPERATION_FAILED,       /**< 操作失败 */
    MCU_UART_ERROR_MAX                     /**< 错误码最大值 */
} mcu_uart_error_t;

/* ========== 设备和通道定义 ========== */

/** @brief 设备类型枚举 */
typedef enum mcu_uart_device_type_e {
    MCU_UART_DEVICE_HEAD_UNIT = 0,         /**< 头单元设备 */
    MCU_UART_DEVICE_WIRED_REMOTE,          /**< 有线遥控设备 */
    MCU_UART_DEVICE_WIRELESS_REMOTE,       /**< 无线遥控设备 */
    MCU_UART_DEVICE_UNKNOWN,               /**< 未知设备类型 */
    MCU_UART_DEVICE_MAX
} mcu_uart_device_type_t;

/** @brief UART通道枚举 */
typedef enum mcu_uart_channel_e {
    MCU_UART_CHANNEL_MAIN = 0,             /**< 主通道(UART3) */
    MCU_UART_CHANNEL_REMOTE,               /**< 遥控器通道(UART1) */
    MCU_UART_CHANNEL_DEBUG,                /**< 调试通道 */
    MCU_UART_CHANNEL_MAX
} mcu_uart_channel_t;

/** @brief 设备状态枚举 */
typedef enum mcu_uart_device_state_e {
    MCU_UART_DEVICE_STATE_UNINITIALIZED = 0, /**< 未初始化 */
    MCU_UART_DEVICE_STATE_INITIALIZING,      /**< 初始化中 */
    MCU_UART_DEVICE_STATE_READY,             /**< 就绪 */
    MCU_UART_DEVICE_STATE_RUNNING,           /**< 运行中 */
    MCU_UART_DEVICE_STATE_SUSPENDED,         /**< 挂起 */
    MCU_UART_DEVICE_STATE_ERROR,             /**< 错误状态 */
    MCU_UART_DEVICE_STATE_MAX
} mcu_uart_device_state_t;

/* ========== 协议常量定义 ========== */

/** @brief 协议标签定义 */
#define MCU_UART_SEND_TAG               0xAA55      /**< 发送标签 */
#define MCU_UART_RECV_TAG               0x55AA      /**< 接收标签 */
#define MCU_UART_UART0_TAG              0xA5        /**< UART0标签 */
#define MCU_UART_END_TAG                0x0D0A      /**< 结束标签 */

/** @brief 包头长度定义 */
#ifdef REMOTE_ADDR_4BYTES
#define MCU_UART_REMOTE_HEAD_LEN        11          /**< 远程包头长度(4字节地址) */
#define MCU_UART_SUB_HEAD_LEN           9           /**< 子包头长度 */
#else
#define MCU_UART_REMOTE_HEAD_LEN        9           /**< 远程包头长度(2字节地址) */
#define MCU_UART_SUB_HEAD_LEN           7           /**< 子包头长度 */
#endif

#define MCU_UART_HEAD_LEN               5           /**< 标准包头长度 */
#define MCU_UART_END_LEN                2           /**< 结束符长度 */
#define MCU_UART_CHECKSUM_LEN           1           /**< 校验和长度 */

/** @brief 缓冲区大小定义 */
#define MCU_UART_SEQUEL_BUF_SIZE        16384       /**< 续传缓冲区大小 */
#define MCU_UART_ARTWORK_BUF_SIZE       (1024*1024) /**< 图片缓冲区大小 */
#define MCU_UART_MAX_CMD_NUMBER         32          /**< 最大命令数量 */
#define MCU_UART_MAX_PACKET_LEN         1200        /**< 最大包长度 */
#define MCU_UART_MAX_BT_CMD_LEN         576         /**< 最大蓝牙命令长度 */
#define MCU_UART_RX_BUFFER_SIZE         4096        /**< 接收缓冲区大小 */
#define MCU_UART_TX_BUFFER_SIZE         2048        /**< 发送缓冲区大小 */

/** @brief 超时配置 */
#define MCU_UART_ACK_TIMEOUT_MS         50          /**< ACK超时时间 */
#define MCU_UART_RESEND_COUNT           5           /**< 重传次数 */
#define MCU_UART_COMMAND_TIMEOUT_MS     1000        /**< 命令超时时间 */
#define MCU_UART_INIT_TIMEOUT_MS        5000        /**< 初始化超时时间 */

/** @brief 地址定义 */
#define MCU_UART_ALL_REMOTE_DEVICE      0x0000      /**< 所有远程设备地址 */
#define MCU_UART_BROADCAST_ADDRESS      0xFFFF      /**< 广播地址 */

/* ========== 数据结构定义 ========== */

/**
 * @brief 设备地址类型
 */
#ifdef REMOTE_ADDR_4BYTES
typedef __u32 mcu_uart_device_addr_t;
#else
typedef __u16 mcu_uart_device_addr_t;
#endif

/**
 * @brief 续传缓冲区结构(保持与原有兼容)
 * @note 基于原有uart_sequel_t结构的增强版本
 */
typedef struct mcu_uart_sequel_s {
    /* 基础字段(保持原有兼容性) */
    __u8 *buf;                          /**< 缓冲区基址 */
    __u8 *start;                        /**< 起始指针 */
    __u8 *end;                          /**< 结束指针 */
    __u8 checksum;                      /**< 校验和 */
    __u32 len;                          /**< 命令总长度 */
    
    /* 循环缓冲区字段(保持原有兼容性) */
    __u32 uBufSize;                     /**< 缓冲区大小 */
    __u8 *pRdPtr;                       /**< 读指针 */
    __u8 *pWrPtr;                       /**< 写指针 */
    __u32 uDataSize;                    /**< 数据大小 */
    __u32 uFreeSize;                    /**< 空闲空间 */
    
    /* 增强字段(新增) */
    bool is_initialized;                /**< 是否已初始化 */
    __u32 magic;                        /**< 魔数验证 */
    __u32 overflow_count;               /**< 溢出次数 */
    __u32 underflow_count;              /**< 下溢次数 */
    __u32 last_access_time;             /**< 最后访问时间 */
    void *lock;                         /**< 锁对象 */
} mcu_uart_sequel_t;

/**
 * @brief 远程设备包头结构(保持与原有兼容)
 * @note 基于原有RemotePkgHead_t结构
 */
typedef struct mcu_uart_remote_pkg_head_s {
    mcu_uart_device_addr_t device_addr; /**< 设备地址 */
    __u8 zone_mask;                     /**< 区域掩码 */
    __u8 ack;                           /**< 确认标志 */
} mcu_uart_remote_pkg_head_t;

/**
 * @brief ACK等待包头结构(保持与原有兼容)
 * @note 基于原有WaitAckPkgHead_t结构
 */
typedef struct mcu_uart_wait_ack_head_s {
    __u16 wait_ack_len;                 /**< 等待ACK的包长度 */
    __u8 wait_cnt;                      /**< 等待计数(10ms为单位) */
} mcu_uart_wait_ack_head_t;

/**
 * @brief 图片缓冲区结构(保持与原有兼容)
 * @note 基于原有ArtworkBuf_t结构
 */
typedef struct mcu_uart_artwork_buf_s {
    __u8 *buf;                          /**< 缓冲区基址 */
    __u8 *start;                        /**< 起始指针 */
    __u8 *end;                          /**< 结束指针 */
    __u32 len;                          /**< 数据长度 */
    
    /* 增强字段 */
    __u32 total_size;                   /**< 总大小 */
    __u32 received_size;                /**< 已接收大小 */
    __u16 packet_count;                 /**< 包数量 */
    __u16 received_packets;             /**< 已接收包数 */
    __u8 format_type;                   /**< 格式类型 */
    bool is_compressed;                 /**< 是否压缩 */
    __u32 artwork_id;                   /**< 图片ID */
} mcu_uart_artwork_buf_t;

/**
 * @brief 图片包结构(保持与原有兼容)
 * @note 基于原有Artwork_package_t结构
 */
typedef struct mcu_uart_artwork_package_s {
    __u8 type1;                         /**< 类型1 */
    __u8 type2;                         /**< 类型2 */
    __u32 page_total;                   /**< 总页数 */
    __u32 page_cur;                     /**< 当前页 */
    __u32 page_last;                    /**< 最后页 */
    __u32 len;                          /**< 数据长度 */
    __u32 remain_len;                   /**< 剩余长度 */
} mcu_uart_artwork_package_t;

/**
 * @brief 命令队列结构(保持与原有兼容)
 * @note 基于原有cmd_queue_t结构
 */
typedef struct mcu_uart_cmd_queue_s {
    __u8 command[MCU_UART_MAX_CMD_NUMBER]; /**< 命令缓冲区 */
    __u8 *start;                        /**< 队列头指针 */
    __u8 *tail;                         /**< 队列尾指针 */
    __u8 num;                           /**< 当前命令数量 */
    
    /* 增强字段 */
    __u32 max_size;                     /**< 最大队列大小 */
    __u32 overflow_count;               /**< 溢出次数 */
    __u32 total_processed;              /**< 总处理数量 */
    void *lock;                         /**< 锁对象 */
} mcu_uart_cmd_queue_t;

/* ========== 工具宏定义(保持与原有兼容) ========== */

/** @brief 字节操作宏 */
#define MCU_UART_LOBYTE(w)              ((__u8)(w))
#define MCU_UART_HIBYTE(w)              ((__u8)(((__u16)(w) >> 8) & 0xFF))
#define MCU_UART_MAKEWORD(high, low)    ((__u16)(((__u8)(low)) | (((__u16)((__u8)(high))) << 8)))
#define MCU_UART_MAKEINT(h1,h2,l1,l2)   ((__u32)(((__u8)(l2)) | (((__u32)((__u8)(l1))) << 8)) | (((__u32)((__u8)(h2))) << 16)| (((__u32)((__u8)(h1))) << 24))

/** @brief 魔数定义 */
#define MCU_UART_MAGIC_SEQUEL           0x5A5A5A5A  /**< 续传缓冲区魔数 */
#define MCU_UART_MAGIC_ARTWORK          0xA5A5A5A5  /**< 图片缓冲区魔数 */
#define MCU_UART_MAGIC_QUEUE            0x3C3C3C3C  /**< 命令队列魔数 */
#define MCU_UART_MAGIC_HANDLE           0x12345678  /**< 句柄魔数 */

/** @brief 内存操作宏(保持兼容) */
#define MCU_UART_MEMSET(pmem, val, size) eLIBs_memset(pmem, val, size)
#define MCU_UART_MEMCPY(dst, src, size)  eLIBs_memcpy(dst, src, size)
#define MCU_UART_MEMCMP(p1, p2, size)    eLIBs_memcmp(p1, p2, size)

/** @brief 安全检查宏 */
#define MCU_UART_CHECK_POINTER(ptr)     ((ptr) != NULL)
#define MCU_UART_CHECK_MAGIC(obj, magic) ((obj) && (obj)->magic == (magic))
#define MCU_UART_CHECK_RANGE(val, min, max) ((val) >= (min) && (val) <= (max))

/* ========== 回调函数类型定义 ========== */

/**
 * @brief 数据处理回调函数类型
 * @param[in] data 数据指针
 * @param[in] len 数据长度
 * @param[in] context 用户上下文
 * @return 错误码
 */
typedef mcu_uart_error_t (*mcu_uart_data_handler_f)(
    const void *data, 
    __u32 len, 
    void *context
);

/**
 * @brief 错误处理回调函数类型
 * @param[in] error_code 错误码
 * @param[in] context 用户上下文
 */
typedef void (*mcu_uart_error_handler_f)(
    mcu_uart_error_t error_code, 
    void *context
);

/**
 * @brief 命令处理回调函数类型
 * @param[in] command 命令码
 * @param[in] data 命令数据
 * @param[in] len 数据长度
 * @param[in] context 用户上下文
 * @return 错误码
 */
typedef mcu_uart_error_t (*mcu_uart_command_handler_f)(
    __u16 command,
    const void *data,
    __u32 len,
    void *context
);

/**
 * @brief 状态变化回调函数类型
 * @param[in] old_state 旧状态
 * @param[in] new_state 新状态
 * @param[in] context 用户上下文
 */
typedef void (*mcu_uart_state_change_handler_f)(
    mcu_uart_device_state_t old_state,
    mcu_uart_device_state_t new_state,
    void *context
);

/* ========== 条件编译兼容性定义 ========== */

#ifdef GET_CMD_NO_ENABLE
    #define MCU_UART_CMD_NO_ENABLED     1
#else
    #define MCU_UART_CMD_NO_ENABLED     0
#endif

#ifdef ACK_WAIT_ENABLE
    #define MCU_UART_ACK_WAIT_ENABLED   1
#else
    #define MCU_UART_ACK_WAIT_ENABLED   0
#endif

#ifdef REMOTE_ADDR_4BYTES
    #define MCU_UART_ADDR_4BYTES        1
#else
    #define MCU_UART_ADDR_4BYTES        0
#endif

/* ========== 向前兼容性定义 ========== */

/** @brief 保持与原有类型的兼容性 */
typedef mcu_uart_sequel_t               uart_sequel_t;
typedef mcu_uart_remote_pkg_head_t      RemotePkgHead_t;
typedef mcu_uart_wait_ack_head_t        WaitAckPkgHead_t;
typedef mcu_uart_artwork_buf_t          ArtworkBuf_t;
typedef mcu_uart_artwork_package_t      Artwork_package_t;
typedef mcu_uart_cmd_queue_t            cmd_queue_t;

/** @brief 保持与原有宏的兼容性 */
#define LOBYTE                          MCU_UART_LOBYTE
#define HIBYTE                          MCU_UART_HIBYTE
#define MAKEWORD                        MCU_UART_MAKEWORD
#define MAKEINT                         MCU_UART_MAKEINT

#define SEQUEL_BUF_SIZE                 MCU_UART_SEQUEL_BUF_SIZE
#define ARTWORK_BUF_SIZE                MCU_UART_ARTWORK_BUF_SIZE
#define MAX_CMD_NUMBER                  MCU_UART_MAX_CMD_NUMBER
#define MAX_UART_PACKGE_LEN             MCU_UART_MAX_PACKET_LEN
#define MAX_BT_CMD_LENGTH               MCU_UART_MAX_BT_CMD_LEN

#define SEND_CMD_TAG                    MCU_UART_SEND_TAG
#define GET_CMD_TAG                     MCU_UART_RECV_TAG
#define GET_UART0_CMD_TAG               MCU_UART_UART0_TAG

#define CMD_REMOTE_HEAD_LEN             MCU_UART_REMOTE_HEAD_LEN
#define CMD_SUB_HEAD_LEN                MCU_UART_SUB_HEAD_LEN
#define CMD_HEAD_LEN                    MCU_UART_HEAD_LEN
#define CMD_END_LEN                     MCU_UART_END_LEN
#define CMD_CHUCKSUM_LEN                MCU_UART_CHECKSUM_LEN

#define ALL_REMOTE_DEVICE               MCU_UART_ALL_REMOTE_DEVICE
#define WAIT_ACK_CNT_RESEND             (MCU_UART_ACK_TIMEOUT_MS/10)

#ifdef __cplusplus
}
#endif

#endif /* MCU_UART_TYPES_H */
