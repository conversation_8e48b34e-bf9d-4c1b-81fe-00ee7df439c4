/**
 * @file mcu_uart_core.c
 * @brief MCU UART通信模块核心管理实现
 * @version 1.0
 * @date 2024-01-20
 * 
 * @copyright Copyright (c) 2024 Allwinner Technology Co., Ltd.
 * 
 * @details
 * 核心管理模块负责：
 * - 模块整体初始化和反初始化
 * - 各子模块的协调管理
 * - 系统线程创建和管理
 * - 全局状态管理
 * - 错误处理和恢复
 */

#include "mcu_uart.h"
#include "internal/mcu_uart_internal.h"
#include "internal/mcu_protocol_internal.h"
#include "internal/mcu_transport_internal.h"
#include "internal/mcu_command_internal.h"
#include "internal/mcu_device_internal.h"

#include <rtthread.h>
#include <rtdevice.h>
#include <string.h>

/* ================================ 私有宏定义 ================================ */

#define MCU_UART_THREAD_STACK_SIZE      (4096)
#define MCU_UART_THREAD_PRIORITY        (12)
#define MCU_UART_THREAD_TIMESLICE       (10)
#define MCU_UART_THREAD_NAME            "mcu_uart"

#define MCU_UART_MAIN_LOOP_DELAY_MS     (10)
#define MCU_UART_SHUTDOWN_TIMEOUT_MS    (5000)

/* ================================ 私有数据结构 ================================ */

/**
 * @brief MCU UART模块全局上下文
 */
typedef struct mcu_uart_global_context {
    mcu_uart_state_e state;                     ///< 模块状态
    mcu_uart_config_t config;                   ///< 配置参数
    
    /* 子模块句柄 */
    mcu_protocol_handle_t protocol_handle;      ///< 协议处理句柄
    mcu_transport_handle_t transport_handle;    ///< 传输层句柄
    mcu_command_handle_t command_handle;        ///< 命令处理句柄
    mcu_device_handle_t device_handle;          ///< 设备层句柄
    
    /* 线程同步对象 */
    rt_thread_t main_thread;                    ///< 主处理线程
    rt_sem_t startup_sem;                       ///< 启动信号量
    rt_sem_t shutdown_sem;                      ///< 关闭信号量
    rt_mutex_t state_mutex;                     ///< 状态保护锁
    
    /* 运行时状态 */
    volatile bool thread_running;               ///< 线程运行标志
    volatile bool shutdown_requested;           ///< 关闭请求标志
    
    /* 统计信息 */
    mcu_uart_stats_t stats;                     ///< 统计数据
    
} mcu_uart_global_context_t;

/* ================================ 静态变量 ================================ */

static mcu_uart_global_context_t *g_mcu_uart_ctx = NULL;
static bool g_mcu_uart_initialized = false;

/* ================================ 私有函数声明 ================================ */

static mcu_uart_result_e mcu_uart_validate_config(const mcu_uart_config_t *config);
static mcu_uart_result_e mcu_uart_create_sync_objects(mcu_uart_global_context_t *ctx);
static void mcu_uart_destroy_sync_objects(mcu_uart_global_context_t *ctx);
static mcu_uart_result_e mcu_uart_start_thread(mcu_uart_global_context_t *ctx);
static mcu_uart_result_e mcu_uart_stop_thread(mcu_uart_global_context_t *ctx);
static void mcu_uart_main_thread_entry(void *parameter);
static mcu_uart_result_e mcu_uart_process_main_loop(mcu_uart_global_context_t *ctx);
static mcu_uart_result_e mcu_uart_change_state(mcu_uart_global_context_t *ctx, 
                                               mcu_uart_state_e new_state);

/* ================================ 公共函数实现 ================================ */

/**
 * @brief 初始化MCU UART通信模块
 * 
 * @param config 配置参数
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_uart_init(const mcu_uart_config_t *config)
{
    mcu_uart_result_e result = MCU_UART_OK;
    mcu_uart_global_context_t *ctx = NULL;
    
    /* 检查输入参数 */
    if (config == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 检查是否已初始化 */
    if (g_mcu_uart_initialized) {
        return MCU_UART_ERROR_ALREADY_INITIALIZED;
    }
    
    /* 验证配置参数 */
    result = mcu_uart_validate_config(config);
    if (result != MCU_UART_OK) {
        return result;
    }
    
    /* 分配全局上下文 */
    ctx = (mcu_uart_global_context_t *)rt_malloc(sizeof(mcu_uart_global_context_t));
    if (ctx == NULL) {
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    /* 初始化上下文 */
    memset(ctx, 0, sizeof(mcu_uart_global_context_t));
    memcpy(&ctx->config, config, sizeof(mcu_uart_config_t));
    ctx->state = MCU_UART_STATE_IDLE;
    ctx->thread_running = false;
    ctx->shutdown_requested = false;
    
    /* 创建同步对象 */
    result = mcu_uart_create_sync_objects(ctx);
    if (result != MCU_UART_OK) {
        rt_free(ctx);
        return result;
    }
    
    /* 初始化设备层 */
    result = mcu_device_init(&ctx->device_handle, &config->device_config);
    if (result != MCU_UART_OK) {
        mcu_uart_destroy_sync_objects(ctx);
        rt_free(ctx);
        return result;
    }
    
    /* 初始化传输层 */
    result = mcu_transport_init(&ctx->transport_handle, &config->transport_config);
    if (result != MCU_UART_OK) {
        mcu_device_deinit(ctx->device_handle);
        mcu_uart_destroy_sync_objects(ctx);
        rt_free(ctx);
        return result;
    }
    
    /* 初始化协议层 */
    result = mcu_protocol_init(&ctx->protocol_handle, &config->protocol_config);
    if (result != MCU_UART_OK) {
        mcu_transport_deinit(ctx->transport_handle);
        mcu_device_deinit(ctx->device_handle);
        mcu_uart_destroy_sync_objects(ctx);
        rt_free(ctx);
        return result;
    }
    
    /* 初始化命令处理层 */
    result = mcu_command_init(&ctx->command_handle, &config->command_config);
    if (result != MCU_UART_OK) {
        mcu_protocol_deinit(ctx->protocol_handle);
        mcu_transport_deinit(ctx->transport_handle);
        mcu_device_deinit(ctx->device_handle);
        mcu_uart_destroy_sync_objects(ctx);
        rt_free(ctx);
        return result;
    }
    
    /* 启动主处理线程 */
    result = mcu_uart_start_thread(ctx);
    if (result != MCU_UART_OK) {
        mcu_command_deinit(ctx->command_handle);
        mcu_protocol_deinit(ctx->protocol_handle);
        mcu_transport_deinit(ctx->transport_handle);
        mcu_device_deinit(ctx->device_handle);
        mcu_uart_destroy_sync_objects(ctx);
        rt_free(ctx);
        return result;
    }
    
    /* 设置全局上下文 */
    g_mcu_uart_ctx = ctx;
    g_mcu_uart_initialized = true;
    
    /* 等待线程启动 */
    rt_sem_take(ctx->startup_sem, RT_WAITING_FOREVER);
    
    /* 切换到运行状态 */
    mcu_uart_change_state(ctx, MCU_UART_STATE_RUNNING);
    
    return MCU_UART_OK;
}

/**
 * @brief 反初始化MCU UART通信模块
 * 
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_uart_deinit(void)
{
    mcu_uart_global_context_t *ctx = g_mcu_uart_ctx;
    
    /* 检查是否已初始化 */
    if (!g_mcu_uart_initialized || ctx == NULL) {
        return MCU_UART_ERROR_NOT_INITIALIZED;
    }
    
    /* 切换到停止状态 */
    mcu_uart_change_state(ctx, MCU_UART_STATE_STOPPING);
    
    /* 停止主处理线程 */
    mcu_uart_stop_thread(ctx);
    
    /* 反初始化各子模块 */
    if (ctx->command_handle) {
        mcu_command_deinit(ctx->command_handle);
        ctx->command_handle = NULL;
    }
    
    if (ctx->protocol_handle) {
        mcu_protocol_deinit(ctx->protocol_handle);
        ctx->protocol_handle = NULL;
    }
    
    if (ctx->transport_handle) {
        mcu_transport_deinit(ctx->transport_handle);
        ctx->transport_handle = NULL;
    }
    
    if (ctx->device_handle) {
        mcu_device_deinit(ctx->device_handle);
        ctx->device_handle = NULL;
    }
    
    /* 销毁同步对象 */
    mcu_uart_destroy_sync_objects(ctx);
    
    /* 释放上下文内存 */
    rt_free(ctx);
    
    /* 清除全局状态 */
    g_mcu_uart_ctx = NULL;
    g_mcu_uart_initialized = false;
    
    return MCU_UART_OK;
}

/**
 * @brief 获取模块当前状态
 * 
 * @return mcu_uart_state_e 
 */
mcu_uart_state_e mcu_uart_get_state(void)
{
    mcu_uart_global_context_t *ctx = g_mcu_uart_ctx;
    mcu_uart_state_e state;
    
    if (!g_mcu_uart_initialized || ctx == NULL) {
        return MCU_UART_STATE_UNINITIALIZED;
    }
    
    rt_mutex_take(ctx->state_mutex, RT_WAITING_FOREVER);
    state = ctx->state;
    rt_mutex_release(ctx->state_mutex);
    
    return state;
}

/**
 * @brief 获取模块配置
 * 
 * @param config 配置参数输出缓冲区
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_uart_get_config(mcu_uart_config_t *config)
{
    mcu_uart_global_context_t *ctx = g_mcu_uart_ctx;
    
    if (config == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (!g_mcu_uart_initialized || ctx == NULL) {
        return MCU_UART_ERROR_NOT_INITIALIZED;
    }
    
    rt_mutex_take(ctx->state_mutex, RT_WAITING_FOREVER);
    memcpy(config, &ctx->config, sizeof(mcu_uart_config_t));
    rt_mutex_release(ctx->state_mutex);
    
    return MCU_UART_OK;
}

/**
 * @brief 获取统计信息
 * 
 * @param stats 统计信息输出缓冲区
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_uart_get_stats(mcu_uart_stats_t *stats)
{
    mcu_uart_global_context_t *ctx = g_mcu_uart_ctx;
    
    if (stats == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (!g_mcu_uart_initialized || ctx == NULL) {
        return MCU_UART_ERROR_NOT_INITIALIZED;
    }
    
    rt_mutex_take(ctx->state_mutex, RT_WAITING_FOREVER);
    memcpy(stats, &ctx->stats, sizeof(mcu_uart_stats_t));
    rt_mutex_release(ctx->state_mutex);
    
    return MCU_UART_OK;
}

/* ================================ 私有函数实现 ================================ */

/**
 * @brief 验证配置参数
 * 
 * @param config 配置参数
 * @return mcu_uart_result_e 
 */
static mcu_uart_result_e mcu_uart_validate_config(const mcu_uart_config_t *config)
{
    /* 检查基本参数 */
    if (config->main_uart_channel >= MCU_UART_CHANNEL_MAX ||
        config->remote_uart_channel >= MCU_UART_CHANNEL_MAX) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 检查UART通道不能相同 */
    if (config->main_uart_channel == config->remote_uart_channel) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 检查回调函数 */
    if (config->event_callback == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    return MCU_UART_OK;
}

/**
 * @brief 创建同步对象
 * 
 * @param ctx 上下文
 * @return mcu_uart_result_e 
 */
static mcu_uart_result_e mcu_uart_create_sync_objects(mcu_uart_global_context_t *ctx)
{
    /* 创建启动信号量 */
    ctx->startup_sem = rt_sem_create("mcu_startup", 0, RT_IPC_FLAG_FIFO);
    if (ctx->startup_sem == NULL) {
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    /* 创建关闭信号量 */
    ctx->shutdown_sem = rt_sem_create("mcu_shutdown", 0, RT_IPC_FLAG_FIFO);
    if (ctx->shutdown_sem == NULL) {
        rt_sem_delete(ctx->startup_sem);
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    /* 创建状态保护锁 */
    ctx->state_mutex = rt_mutex_create("mcu_state", RT_IPC_FLAG_FIFO);
    if (ctx->state_mutex == NULL) {
        rt_sem_delete(ctx->startup_sem);
        rt_sem_delete(ctx->shutdown_sem);
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    return MCU_UART_OK;
}

/**
 * @brief 销毁同步对象
 * 
 * @param ctx 上下文
 */
static void mcu_uart_destroy_sync_objects(mcu_uart_global_context_t *ctx)
{
    if (ctx->startup_sem) {
        rt_sem_delete(ctx->startup_sem);
        ctx->startup_sem = NULL;
    }
    
    if (ctx->shutdown_sem) {
        rt_sem_delete(ctx->shutdown_sem);
        ctx->shutdown_sem = NULL;
    }
    
    if (ctx->state_mutex) {
        rt_mutex_delete(ctx->state_mutex);
        ctx->state_mutex = NULL;
    }
}

/**
 * @brief 启动主处理线程
 * 
 * @param ctx 上下文
 * @return mcu_uart_result_e 
 */
static mcu_uart_result_e mcu_uart_start_thread(mcu_uart_global_context_t *ctx)
{
    /* 创建主处理线程 */
    ctx->main_thread = rt_thread_create(MCU_UART_THREAD_NAME,
                                        mcu_uart_main_thread_entry,
                                        ctx,
                                        MCU_UART_THREAD_STACK_SIZE,
                                        MCU_UART_THREAD_PRIORITY,
                                        MCU_UART_THREAD_TIMESLICE);
    
    if (ctx->main_thread == NULL) {
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    /* 启动线程 */
    rt_thread_startup(ctx->main_thread);
    
    return MCU_UART_OK;
}

/**
 * @brief 停止主处理线程
 * 
 * @param ctx 上下文
 * @return mcu_uart_result_e 
 */
static mcu_uart_result_e mcu_uart_stop_thread(mcu_uart_global_context_t *ctx)
{
    rt_err_t ret;
    
    if (ctx->main_thread == NULL) {
        return MCU_UART_OK;
    }
    
    /* 请求线程关闭 */
    ctx->shutdown_requested = true;
    
    /* 等待线程关闭 */
    ret = rt_sem_take(ctx->shutdown_sem, rt_tick_from_millisecond(MCU_UART_SHUTDOWN_TIMEOUT_MS));
    if (ret != RT_EOK) {
        /* 强制删除线程 */
        rt_thread_delete(ctx->main_thread);
    }
    
    ctx->main_thread = NULL;
    ctx->thread_running = false;
    
    return MCU_UART_OK;
}

/**
 * @brief 主处理线程入口函数
 * 
 * @param parameter 线程参数
 */
static void mcu_uart_main_thread_entry(void *parameter)
{
    mcu_uart_global_context_t *ctx = (mcu_uart_global_context_t *)parameter;
    mcu_uart_result_e result;
    
    ctx->thread_running = true;
    
    /* 通知初始化完成 */
    rt_sem_release(ctx->startup_sem);
    
    /* 主循环 */
    while (!ctx->shutdown_requested) {
        /* 处理主循环逻辑 */
        result = mcu_uart_process_main_loop(ctx);
        if (result != MCU_UART_OK) {
            /* 错误处理 */
            mcu_uart_change_state(ctx, MCU_UART_STATE_ERROR);
        }
        
        /* 短暂延时 */
        rt_thread_mdelay(MCU_UART_MAIN_LOOP_DELAY_MS);
    }
    
    ctx->thread_running = false;
    
    /* 通知关闭完成 */
    rt_sem_release(ctx->shutdown_sem);
}

/**
 * @brief 处理主循环逻辑
 * 
 * @param ctx 上下文
 * @return mcu_uart_result_e 
 */
static mcu_uart_result_e mcu_uart_process_main_loop(mcu_uart_global_context_t *ctx)
{
    mcu_uart_result_e result;
    
    /* 处理接收数据 */
    result = mcu_transport_process_receive(ctx->transport_handle);
    if (result != MCU_UART_OK && result != MCU_UART_ERROR_NO_DATA) {
        return result;
    }
    
    /* 处理协议解析 */
    result = mcu_protocol_process_packets(ctx->protocol_handle);
    if (result != MCU_UART_OK && result != MCU_UART_ERROR_NO_DATA) {
        return result;
    }
    
    /* 处理命令执行 */
    result = mcu_command_process_queue(ctx->command_handle);
    if (result != MCU_UART_OK && result != MCU_UART_ERROR_NO_DATA) {
        return result;
    }
    
    /* 处理发送数据 */
    result = mcu_transport_process_transmit(ctx->transport_handle);
    if (result != MCU_UART_OK && result != MCU_UART_ERROR_NO_DATA) {
        return result;
    }
    
    return MCU_UART_OK;
}

/**
 * @brief 切换模块状态
 * 
 * @param ctx 上下文
 * @param new_state 新状态
 * @return mcu_uart_result_e 
 */
static mcu_uart_result_e mcu_uart_change_state(mcu_uart_global_context_t *ctx, 
                                               mcu_uart_state_e new_state)
{
    mcu_uart_state_e old_state;
    
    rt_mutex_take(ctx->state_mutex, RT_WAITING_FOREVER);
    old_state = ctx->state;
    ctx->state = new_state;
    rt_mutex_release(ctx->state_mutex);
    
    /* 触发状态变化事件 */
    if (ctx->config.event_callback && old_state != new_state) {
        mcu_uart_event_t event = {
            .type = MCU_UART_EVENT_STATE_CHANGED,
            .data.state_change.old_state = old_state,
            .data.state_change.new_state = new_state
        };
        ctx->config.event_callback(&event, ctx->config.user_data);
    }
    
    return MCU_UART_OK;
} 