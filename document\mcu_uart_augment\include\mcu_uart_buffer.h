/**
 * @file mcu_uart_buffer.h
 * @brief MCU UART增强版缓冲区管理模块
 * @details 高性能环形缓冲区、内存池和缓冲区管理系统
 * @version 1.0.0
 * @date 2025-01-19
 */

#ifndef MCU_UART_BUFFER_H
#define MCU_UART_BUFFER_H

#ifdef __cplusplus
extern "C" {
#endif

#include "mcu_uart_types.h"
#include "mcu_uart_config.h"

/* ========== 缓冲区类型定义 ========== */

/** @brief 缓冲区状态枚举 */
typedef enum mcu_uart_buffer_state_e {
    MCU_UART_BUFFER_STATE_UNINITIALIZED = 0, /**< 未初始化 */
    MCU_UART_BUFFER_STATE_EMPTY,             /**< 空闲状态 */
    MCU_UART_BUFFER_STATE_FILLING,           /**< 正在填充 */
    MCU_UART_BUFFER_STATE_FULL,              /**< 已满 */
    MCU_UART_BUFFER_STATE_READING,           /**< 正在读取 */
    MCU_UART_BUFFER_STATE_OVERFLOW,          /**< 溢出状态 */
    MCU_UART_BUFFER_STATE_ERROR,             /**< 错误状态 */
    MCU_UART_BUFFER_STATE_MAX
} mcu_uart_buffer_state_t;

/** @brief 缓冲区类型枚举 */
typedef enum mcu_uart_buffer_type_e {
    MCU_UART_BUFFER_TYPE_RX = 0,             /**< 接收缓冲区 */
    MCU_UART_BUFFER_TYPE_TX,                 /**< 发送缓冲区 */
    MCU_UART_BUFFER_TYPE_CMD,                /**< 命令缓冲区 */
    MCU_UART_BUFFER_TYPE_SEQUEL,             /**< 续传缓冲区 */
    MCU_UART_BUFFER_TYPE_ARTWORK,            /**< 艺术品缓冲区 */
    MCU_UART_BUFFER_TYPE_DEBUG,              /**< 调试缓冲区 */
    MCU_UART_BUFFER_TYPE_MAX
} mcu_uart_buffer_type_t;

/** @brief 缓冲区操作模式 */
typedef enum mcu_uart_buffer_mode_e {
    MCU_UART_BUFFER_MODE_BLOCKING = 0,       /**< 阻塞模式 */
    MCU_UART_BUFFER_MODE_NON_BLOCKING,       /**< 非阻塞模式 */
    MCU_UART_BUFFER_MODE_OVERWRITE,          /**< 覆写模式 */
    MCU_UART_BUFFER_MODE_MAX
} mcu_uart_buffer_mode_t;

/* ========== 缓冲区结构定义 ========== */

/**
 * @brief 环形缓冲区结构
 */
typedef struct mcu_uart_ring_buffer_s {
    __u8 *data;                              /**< 数据缓冲区 */
    __u32 size;                              /**< 缓冲区总大小 */
    volatile __u32 head;                     /**< 头指针(写入位置) */
    volatile __u32 tail;                     /**< 尾指针(读取位置) */
    volatile __u32 count;                    /**< 当前数据量 */
    
    /* 统计信息 */
    __u32 max_count;                         /**< 历史最大数据量 */
    __u32 total_written;                     /**< 总写入字节数 */
    __u32 total_read;                        /**< 总读取字节数 */
    __u32 overflow_count;                    /**< 溢出次数 */
    __u32 underflow_count;                   /**< 下溢次数 */
    
    /* 配置和状态 */
    mcu_uart_buffer_state_t state;           /**< 缓冲区状态 */
    mcu_uart_buffer_mode_t mode;             /**< 操作模式 */
    __u32 watermark_high;                    /**< 高水位线 */
    __u32 watermark_low;                     /**< 低水位线 */
    __u32 last_access_time;                  /**< 最后访问时间 */
    
    /* 同步和保护 */
    void *lock;                              /**< 锁对象 */
    __u32 magic;                             /**< 魔数验证 */
} mcu_uart_ring_buffer_t;

/**
 * @brief 内存块结构
 */
typedef struct mcu_uart_memory_block_s {
    void *data;                              /**< 数据指针 */
    __u32 size;                              /**< 块大小 */
    bool is_free;                            /**< 是否空闲 */
    struct mcu_uart_memory_block_s *next;    /**< 下一个块 */
    __u32 magic;                             /**< 魔数验证 */
} mcu_uart_memory_block_t;

/**
 * @brief 内存池结构
 */
typedef struct mcu_uart_memory_pool_s {
    __u8 *pool_data;                         /**< 内存池数据 */
    __u32 pool_size;                         /**< 内存池大小 */
    __u32 block_size;                        /**< 块大小 */
    __u32 block_count;                       /**< 块数量 */
    
    mcu_uart_memory_block_t *free_list;      /**< 空闲块链表 */
    mcu_uart_memory_block_t *used_list;      /**< 已用块链表 */
    
    /* 统计信息 */
    __u32 allocated_blocks;                  /**< 已分配块数 */
    __u32 free_blocks;                       /**< 空闲块数 */
    __u32 max_allocated;                     /**< 最大分配块数 */
    __u32 total_allocations;                 /**< 总分配次数 */
    __u32 total_deallocations;               /**< 总释放次数 */
    __u32 allocation_failures;               /**< 分配失败次数 */
    
    /* 同步和保护 */
    void *lock;                              /**< 锁对象 */
    __u32 magic;                             /**< 魔数验证 */
} mcu_uart_memory_pool_t;

/**
 * @brief 缓冲区管理器结构
 */
typedef struct mcu_uart_buffer_manager_s {
    /* 各类型缓冲区 */
    mcu_uart_ring_buffer_t *rx_buffer;      /**< 接收缓冲区 */
    mcu_uart_ring_buffer_t *tx_buffer;      /**< 发送缓冲区 */
    mcu_uart_ring_buffer_t *cmd_buffer;     /**< 命令缓冲区 */
    mcu_uart_sequel_t *sequel_buffer;       /**< 续传缓冲区(兼容) */
    mcu_uart_artwork_buf_t *artwork_buffer; /**< 艺术品缓冲区(兼容) */
    mcu_uart_ring_buffer_t *debug_buffer;   /**< 调试缓冲区 */
    
    /* 内存管理 */
    mcu_uart_memory_pool_t *memory_pool;    /**< 内存池 */
    __u32 total_memory_used;                 /**< 总内存使用量 */
    __u32 max_memory_limit;                  /**< 最大内存限制 */
    
    /* 配置参数 */
    bool auto_resize_enabled;                /**< 是否启用自动调整大小 */
    __u32 resize_threshold;                  /**< 调整大小阈值(百分比) */
    bool zero_copy_enabled;                  /**< 是否启用零拷贝 */
    
    /* 统计信息 */
    __u32 buffer_switches;                   /**< 缓冲区切换次数 */
    __u32 memory_reallocations;              /**< 内存重分配次数 */
    __u32 gc_runs;                           /**< 垃圾回收运行次数 */
    
    /* 同步和保护 */
    void *global_lock;                       /**< 全局锁 */
    __u32 magic;                             /**< 魔数验证 */
} mcu_uart_buffer_manager_t;

/**
 * @brief 缓冲区统计信息
 */
typedef struct mcu_uart_buffer_stats_s {
    __u32 total_buffers;                     /**< 总缓冲区数 */
    __u32 active_buffers;                    /**< 活跃缓冲区数 */
    __u32 total_memory_used;                 /**< 总内存使用量 */
    __u32 total_memory_allocated;            /**< 总分配内存量 */
    __u32 memory_utilization_percent;        /**< 内存利用率 */
    __u32 average_buffer_usage;              /**< 平均缓冲区使用率 */
    __u32 peak_memory_usage;                 /**< 峰值内存使用量 */
    __u32 total_overflows;                   /**< 总溢出次数 */
    __u32 total_underflows;                  /**< 总下溢次数 */
    __u32 gc_efficiency_percent;             /**< 垃圾回收效率 */
} mcu_uart_buffer_stats_t;

/* ========== 环形缓冲区API ========== */

/**
 * @brief 初始化环形缓冲区
 * @param[out] buffer 缓冲区指针
 * @param[in] data 数据缓冲区
 * @param[in] size 缓冲区大小
 * @param[in] mode 操作模式
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_ring_buffer_init(
    mcu_uart_ring_buffer_t *buffer,
    __u8 *data,
    __u32 size,
    mcu_uart_buffer_mode_t mode
);

/**
 * @brief 销毁环形缓冲区
 * @param[in] buffer 缓冲区指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_ring_buffer_deinit(mcu_uart_ring_buffer_t *buffer);

/**
 * @brief 重置环形缓冲区
 * @param[in] buffer 缓冲区指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_ring_buffer_reset(mcu_uart_ring_buffer_t *buffer);

/**
 * @brief 向环形缓冲区写入数据
 * @param[in] buffer 缓冲区指针
 * @param[in] data 写入数据
 * @param[in] len 数据长度
 * @return 实际写入长度
 */
__u32 mcu_uart_ring_buffer_write(
    mcu_uart_ring_buffer_t *buffer,
    const __u8 *data,
    __u32 len
);

/**
 * @brief 从环形缓冲区读取数据
 * @param[in] buffer 缓冲区指针
 * @param[out] data 读取数据缓冲区
 * @param[in] len 期望读取长度
 * @return 实际读取长度
 */
__u32 mcu_uart_ring_buffer_read(
    mcu_uart_ring_buffer_t *buffer,
    __u8 *data,
    __u32 len
);

/**
 * @brief 从环形缓冲区窥视数据(不移动读指针)
 * @param[in] buffer 缓冲区指针
 * @param[out] data 读取数据缓冲区
 * @param[in] len 期望读取长度
 * @param[in] offset 偏移量
 * @return 实际读取长度
 */
__u32 mcu_uart_ring_buffer_peek(
    mcu_uart_ring_buffer_t *buffer,
    __u8 *data,
    __u32 len,
    __u32 offset
);

/**
 * @brief 跳过环形缓冲区中的数据
 * @param[in] buffer 缓冲区指针
 * @param[in] len 跳过长度
 * @return 实际跳过长度
 */
__u32 mcu_uart_ring_buffer_skip(mcu_uart_ring_buffer_t *buffer, __u32 len);

/**
 * @brief 在环形缓冲区中搜索模式
 * @param[in] buffer 缓冲区指针
 * @param[in] pattern 搜索模式
 * @param[in] pattern_len 模式长度
 * @param[out] position 找到的位置
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_ring_buffer_search(
    mcu_uart_ring_buffer_t *buffer,
    const __u8 *pattern,
    __u32 pattern_len,
    __u32 *position
);

/* ========== 内存池API ========== */

/**
 * @brief 初始化内存池
 * @param[out] pool 内存池指针
 * @param[in] pool_data 内存池数据
 * @param[in] pool_size 内存池大小
 * @param[in] block_size 块大小
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_memory_pool_init(
    mcu_uart_memory_pool_t *pool,
    __u8 *pool_data,
    __u32 pool_size,
    __u32 block_size
);

/**
 * @brief 销毁内存池
 * @param[in] pool 内存池指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_memory_pool_deinit(mcu_uart_memory_pool_t *pool);

/**
 * @brief 从内存池分配内存
 * @param[in] pool 内存池指针
 * @param[in] size 请求大小
 * @return 分配的内存指针，失败返回NULL
 */
void* mcu_uart_memory_pool_alloc(mcu_uart_memory_pool_t *pool, __u32 size);

/**
 * @brief 释放内存到内存池
 * @param[in] pool 内存池指针
 * @param[in] ptr 内存指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_memory_pool_free(mcu_uart_memory_pool_t *pool, void *ptr);

/**
 * @brief 内存池垃圾回收
 * @param[in] pool 内存池指针
 * @return 回收的字节数
 */
__u32 mcu_uart_memory_pool_gc(mcu_uart_memory_pool_t *pool);

/* ========== 缓冲区管理器API ========== */

/**
 * @brief 初始化缓冲区管理器
 * @param[out] manager 管理器指针
 * @param[in] config 配置参数
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_buffer_manager_init(
    mcu_uart_buffer_manager_t *manager,
    const mcu_uart_buffer_config_t *config
);

/**
 * @brief 销毁缓冲区管理器
 * @param[in] manager 管理器指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_buffer_manager_deinit(mcu_uart_buffer_manager_t *manager);

/**
 * @brief 获取指定类型的缓冲区
 * @param[in] manager 管理器指针
 * @param[in] type 缓冲区类型
 * @return 缓冲区指针
 */
void* mcu_uart_buffer_manager_get_buffer(
    mcu_uart_buffer_manager_t *manager,
    mcu_uart_buffer_type_t type
);

/**
 * @brief 执行缓冲区维护
 * @param[in] manager 管理器指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_buffer_manager_maintenance(mcu_uart_buffer_manager_t *manager);

/* ========== 状态查询API ========== */

/**
 * @brief 获取环形缓冲区可用空间
 * @param[in] buffer 缓冲区指针
 * @return 可用空间大小
 */
static inline __u32 mcu_uart_ring_buffer_available_space(const mcu_uart_ring_buffer_t *buffer)
{
    return (buffer) ? (buffer->size - buffer->count) : 0;
}

/**
 * @brief 获取环形缓冲区数据量
 * @param[in] buffer 缓冲区指针
 * @return 数据量
 */
static inline __u32 mcu_uart_ring_buffer_data_count(const mcu_uart_ring_buffer_t *buffer)
{
    return (buffer) ? buffer->count : 0;
}

/**
 * @brief 检查环形缓冲区是否为空
 * @param[in] buffer 缓冲区指针
 * @return true=空, false=非空
 */
static inline bool mcu_uart_ring_buffer_is_empty(const mcu_uart_ring_buffer_t *buffer)
{
    return (buffer) ? (buffer->count == 0) : true;
}

/**
 * @brief 检查环形缓冲区是否已满
 * @param[in] buffer 缓冲区指针
 * @return true=满, false=未满
 */
static inline bool mcu_uart_ring_buffer_is_full(const mcu_uart_ring_buffer_t *buffer)
{
    return (buffer) ? (buffer->count >= buffer->size) : false;
}

/**
 * @brief 获取环形缓冲区使用率(百分比)
 * @param[in] buffer 缓冲区指针
 * @return 使用率(0-100)
 */
__u32 mcu_uart_ring_buffer_usage_percent(const mcu_uart_ring_buffer_t *buffer);

/* ========== 统计和调试API ========== */

/**
 * @brief 获取缓冲区统计信息
 * @param[in] manager 管理器指针
 * @param[out] stats 统计信息结构
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_buffer_get_stats(
    const mcu_uart_buffer_manager_t *manager,
    mcu_uart_buffer_stats_t *stats
);

/**
 * @brief 重置缓冲区统计信息
 * @param[in] manager 管理器指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_buffer_reset_stats(mcu_uart_buffer_manager_t *manager);

/**
 * @brief 打印缓冲区状态信息
 * @param[in] buffer 缓冲区指针
 */
void mcu_uart_ring_buffer_print_status(const mcu_uart_ring_buffer_t *buffer);

/**
 * @brief 打印缓冲区管理器状态
 * @param[in] manager 管理器指针
 */
void mcu_uart_buffer_manager_print_status(const mcu_uart_buffer_manager_t *manager);

/* ========== 向前兼容性定义 ========== */

/** @brief 保持与原有结构的兼容性 */
typedef mcu_uart_ring_buffer_t ring_buffer_t;
typedef mcu_uart_memory_pool_t memory_pool_t;

/** @brief 保持与原有函数的兼容性 */
#define ring_buffer_init                mcu_uart_ring_buffer_init
#define ring_buffer_write               mcu_uart_ring_buffer_write
#define ring_buffer_read                mcu_uart_ring_buffer_read
#define ring_buffer_reset               mcu_uart_ring_buffer_reset

#ifdef __cplusplus
}
#endif

#endif /* MCU_UART_BUFFER_H */
