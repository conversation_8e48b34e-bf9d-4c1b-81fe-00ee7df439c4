/**
 * @file mcu_uart_config.c
 * @brief MCU UART增强版配置管理实现
 * @version 1.0.0
 * @date 2025-01-19
 */

#include "mcu_uart_config.h"
#include <stdlib.h>

/* ========== 全局配置变量 ========== */

static mcu_uart_global_config_t g_current_config = {0};
static bool g_config_initialized = false;

/* ========== 配置管理API实现 ========== */

/**
 * @brief 初始化配置为默认值
 */
mcu_uart_error_t mcu_uart_config_init_default(mcu_uart_global_config_t *config)
{
    if (!config) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 设备类型 */
    config->device_type = MCU_UART_DEVICE_HEAD_UNIT;
    
    /* 硬件配置 */
    config->hardware.uart3_path = "b:\\BUS\\UART3";
    config->hardware.uart1_path = "b:\\BUS\\UART1";
    config->hardware.uart3_baudrate = 115200;
    config->hardware.uart1_baudrate = 115200;
    config->hardware.data_bits = 8;
    config->hardware.stop_bits = 1;
    config->hardware.parity = 0;
    config->hardware.enable_flow_control = false;
    config->hardware.tx_fifo_size = 64;
    config->hardware.rx_fifo_size = 64;
    config->hardware.interrupt_priority = 5;
    
    /* 缓冲区配置 */
    config->buffer.rx_buffer_size = MCU_UART_RX_BUFFER_SIZE;
    config->buffer.tx_buffer_size = MCU_UART_TX_BUFFER_SIZE;
    config->buffer.sequel_buffer_size = MCU_UART_SEQUEL_BUF_SIZE;
    config->buffer.artwork_buffer_size = MCU_UART_ARTWORK_BUF_SIZE;
    config->buffer.cmd_queue_size = MCU_UART_MAX_CMD_NUMBER;
    config->buffer.max_packet_length = MCU_UART_MAX_PACKET_LEN;
    config->buffer.buffer_alignment = 4;
    config->buffer.enable_dynamic_resize = true;
    config->buffer.resize_threshold_percent = 80;
    config->buffer.memory_pool_size = 32768;
    
    /* 协议配置 */
    config->protocol.enable_4byte_address = MCU_UART_ADDR_4BYTES;
    config->protocol.enable_ack_wait = MCU_UART_ACK_WAIT_ENABLED;
    config->protocol.enable_cmd_number = MCU_UART_CMD_NO_ENABLED;
    config->protocol.enable_checksum = true;
    config->protocol.enable_crc = false;
    config->protocol.ack_timeout_ms = MCU_UART_ACK_TIMEOUT_MS;
    config->protocol.max_resend_count = MCU_UART_RESEND_COUNT;
    config->protocol.device_address = MCU_UART_ALL_REMOTE_DEVICE;
    config->protocol.protocol_version = 1;
    config->protocol.packet_timeout_ms = 1000;
    config->protocol.command_timeout_ms = MCU_UART_COMMAND_TIMEOUT_MS;
    
    /* 性能配置 */
    config->performance.enable_zero_copy = MCU_UART_ENABLE_ZERO_COPY;
    config->performance.enable_fast_path = MCU_UART_ENABLE_FAST_PATH;
    config->performance.enable_batch_processing = MCU_UART_ENABLE_BATCH_PROCESSING;
    config->performance.enable_async_processing = MCU_UART_ENABLE_ASYNC_PROCESSING;
    config->performance.batch_size = 16;
    config->performance.processing_thread_priority = 10;
    config->performance.processing_thread_stack_size = 8192;
    config->performance.max_processing_time_ms = 100;
    
    /* 调试配置 */
    config->debug.enable_recv_data_print = false;
    config->debug.enable_send_data_print = false;
    config->debug.enable_cmd_data_debug = false;
    config->debug.enable_performance_monitor = MCU_UART_ENABLE_STATS;
    config->debug.enable_memory_trace = false;
    config->debug.enable_packet_trace = false;
    config->debug.enable_error_injection = false;
    config->debug.debug_level = 2;
    config->debug.max_debug_buffer_size = 4096;
    config->debug.debug_output_path = NULL;
    
    /* 扩展配置 */
    config->extension.enable_rflink_support = false;
    config->extension.enable_custom_protocols = MCU_UART_ENABLE_EXTENSIONS;
    config->extension.enable_plugin_system = false;
    config->extension.max_custom_handlers = 8;
    config->extension.extension_buffer_size = 1024;
    config->extension.extension_context = NULL;
    
    /* 回调函数 */
    config->data_handler = NULL;
    config->error_handler = NULL;
    config->command_handler = NULL;
    config->state_handler = NULL;
    config->callback_context = NULL;
    
    /* 运行时状态 */
    config->is_initialized = false;
    config->magic = 0x5555AAAA;
    config->config_version = 1;
    config->creation_time = ktime_get();
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 验证配置参数有效性
 */
mcu_uart_error_t mcu_uart_config_validate(const mcu_uart_global_config_t *config)
{
    if (!config) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 验证魔数 */
    if (config->magic != 0x5555AAAA) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 验证设备类型 */
    if (config->device_type >= MCU_UART_DEVICE_MAX) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 验证波特率 */
    if (config->hardware.uart3_baudrate == 0 || config->hardware.uart1_baudrate == 0) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 验证缓冲区大小 */
    if (config->buffer.rx_buffer_size == 0 || config->buffer.tx_buffer_size == 0) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 验证超时时间 */
    if (config->protocol.ack_timeout_ms == 0) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 复制配置
 */
mcu_uart_error_t mcu_uart_config_copy(mcu_uart_global_config_t *dst, 
                                      const mcu_uart_global_config_t *src)
{
    if (!dst || !src) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    MCU_UART_MEMCPY(dst, src, sizeof(mcu_uart_global_config_t));
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 获取当前配置
 */
mcu_uart_error_t mcu_uart_config_get_current(mcu_uart_global_config_t *config)
{
    if (!config) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (!g_config_initialized) {
        mcu_uart_config_init_default(&g_current_config);
        g_config_initialized = true;
    }
    
    return mcu_uart_config_copy(config, &g_current_config);
}

/**
 * @brief 设置当前配置
 */
mcu_uart_error_t mcu_uart_config_set_current(const mcu_uart_global_config_t *config)
{
    mcu_uart_error_t ret;
    
    if (!config) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 验证配置 */
    ret = mcu_uart_config_validate(config);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    /* 复制配置 */
    ret = mcu_uart_config_copy(&g_current_config, config);
    if (ret != MCU_UART_ERROR_NONE) {
        return ret;
    }
    
    g_config_initialized = true;
    
    return MCU_UART_ERROR_NONE;
}

/* ========== 配置查询API实现 ========== */

/**
 * @brief 获取设备类型
 */
mcu_uart_device_type_t mcu_uart_config_get_device_type(void)
{
    if (!g_config_initialized) {
        mcu_uart_config_init_default(&g_current_config);
        g_config_initialized = true;
    }
    
    return g_current_config.device_type;
}

/**
 * @brief 获取UART3路径
 */
const char* mcu_uart_config_get_uart3_path(void)
{
    if (!g_config_initialized) {
        mcu_uart_config_init_default(&g_current_config);
        g_config_initialized = true;
    }
    
    return g_current_config.hardware.uart3_path;
}

/**
 * @brief 获取UART1路径
 */
const char* mcu_uart_config_get_uart1_path(void)
{
    if (!g_config_initialized) {
        mcu_uart_config_init_default(&g_current_config);
        g_config_initialized = true;
    }
    
    return g_current_config.hardware.uart1_path;
}

/**
 * @brief 检查是否启用4字节地址
 */
bool mcu_uart_config_is_4byte_address_enabled(void)
{
    if (!g_config_initialized) {
        mcu_uart_config_init_default(&g_current_config);
        g_config_initialized = true;
    }
    
    return g_current_config.protocol.enable_4byte_address;
}

/**
 * @brief 检查是否启用ACK等待
 */
bool mcu_uart_config_is_ack_wait_enabled(void)
{
    if (!g_config_initialized) {
        mcu_uart_config_init_default(&g_current_config);
        g_config_initialized = true;
    }
    
    return g_current_config.protocol.enable_ack_wait;
}

/**
 * @brief 获取ACK超时时间
 */
__u32 mcu_uart_config_get_ack_timeout(void)
{
    if (!g_config_initialized) {
        mcu_uart_config_init_default(&g_current_config);
        g_config_initialized = true;
    }
    
    return g_current_config.protocol.ack_timeout_ms;
}

/**
 * @brief 获取最大重传次数
 */
__u8 mcu_uart_config_get_max_resend_count(void)
{
    if (!g_config_initialized) {
        mcu_uart_config_init_default(&g_current_config);
        g_config_initialized = true;
    }
    
    return g_current_config.protocol.max_resend_count;
}

/**
 * @brief 检查是否启用调试功能
 */
bool mcu_uart_config_is_debug_enabled(void)
{
    if (!g_config_initialized) {
        mcu_uart_config_init_default(&g_current_config);
        g_config_initialized = true;
    }
    
    return (g_current_config.debug.enable_recv_data_print || 
            g_current_config.debug.enable_send_data_print ||
            g_current_config.debug.enable_cmd_data_debug);
}

/**
 * @brief 检查是否启用性能监控
 */
bool mcu_uart_config_is_performance_monitor_enabled(void)
{
    if (!g_config_initialized) {
        mcu_uart_config_init_default(&g_current_config);
        g_config_initialized = true;
    }
    
    return g_current_config.debug.enable_performance_monitor;
}

/**
 * @brief 检查是否启用零拷贝优化
 */
bool mcu_uart_config_is_zero_copy_enabled(void)
{
    if (!g_config_initialized) {
        mcu_uart_config_init_default(&g_current_config);
        g_config_initialized = true;
    }
    
    return g_current_config.performance.enable_zero_copy;
}

/* ========== 配置设置API实现 ========== */

/**
 * @brief 设置设备类型
 */
mcu_uart_error_t mcu_uart_config_set_device_type(
    mcu_uart_global_config_t *config,
    mcu_uart_device_type_t device_type)
{
    if (!config || device_type >= MCU_UART_DEVICE_MAX) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    config->device_type = device_type;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 设置UART硬件参数
 */
mcu_uart_error_t mcu_uart_config_set_uart_params(
    mcu_uart_global_config_t *config,
    __u32 uart3_baudrate,
    __u32 uart1_baudrate)
{
    if (!config || uart3_baudrate == 0 || uart1_baudrate == 0) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    config->hardware.uart3_baudrate = uart3_baudrate;
    config->hardware.uart1_baudrate = uart1_baudrate;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 设置缓冲区大小
 */
mcu_uart_error_t mcu_uart_config_set_buffer_size(
    mcu_uart_global_config_t *config,
    __u32 rx_size,
    __u32 tx_size)
{
    if (!config || rx_size == 0 || tx_size == 0) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    config->buffer.rx_buffer_size = rx_size;
    config->buffer.tx_buffer_size = tx_size;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 设置协议参数
 */
mcu_uart_error_t mcu_uart_config_set_protocol_params(
    mcu_uart_global_config_t *config,
    bool enable_4byte_addr,
    __u32 ack_timeout_ms)
{
    if (!config || ack_timeout_ms == 0) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    config->protocol.enable_4byte_address = enable_4byte_addr;
    config->protocol.ack_timeout_ms = ack_timeout_ms;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 设置调试参数
 */
mcu_uart_error_t mcu_uart_config_set_debug_params(
    mcu_uart_global_config_t *config,
    bool enable_debug,
    __u32 debug_level)
{
    if (!config) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    config->debug.enable_recv_data_print = enable_debug;
    config->debug.enable_send_data_print = enable_debug;
    config->debug.enable_cmd_data_debug = enable_debug;
    config->debug.debug_level = debug_level;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 设置性能参数
 */
mcu_uart_error_t mcu_uart_config_set_performance_params(
    mcu_uart_global_config_t *config,
    bool enable_zero_copy,
    bool enable_async)
{
    if (!config) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    config->performance.enable_zero_copy = enable_zero_copy;
    config->performance.enable_async_processing = enable_async;
    
    return MCU_UART_ERROR_NONE;
}
