/**
 * @file mcu_uart_config.h
 * @brief MCU UART增强版配置管理模块
 * @details 提供完整的配置参数管理，支持运行时配置和编译时优化
 * @version 1.0.0
 * @date 2025-01-19
 */

#ifndef MCU_UART_CONFIG_H
#define MCU_UART_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

#include "mcu_uart_types.h"

/* ========== 编译时配置开关 ========== */

/** @brief 功能开关 */
#ifndef MCU_UART_ENABLE_DEBUG
#define MCU_UART_ENABLE_DEBUG           1    /**< 启用调试功能 */
#endif

#ifndef MCU_UART_ENABLE_STATS
#define MCU_UART_ENABLE_STATS           1    /**< 启用统计功能 */
#endif

#ifndef MCU_UART_ENABLE_COMPAT
#define MCU_UART_ENABLE_COMPAT          1    /**< 启用兼容性支持 */
#endif

#ifndef MCU_UART_ENABLE_ACK
#define MCU_UART_ENABLE_ACK             1    /**< 启用ACK机制 */
#endif

#ifndef MCU_UART_ENABLE_EXTENSIONS
#define MCU_UART_ENABLE_EXTENSIONS      1    /**< 启用扩展功能 */
#endif

#ifndef MCU_UART_ENABLE_FLOW_CONTROL
#define MCU_UART_ENABLE_FLOW_CONTROL    1    /**< 启用流控制 */
#endif

#ifndef MCU_UART_ENABLE_MEMORY_POOL
#define MCU_UART_ENABLE_MEMORY_POOL     1    /**< 启用内存池 */
#endif

#ifndef MCU_UART_ENABLE_ASYNC_PROCESSING
#define MCU_UART_ENABLE_ASYNC_PROCESSING 1   /**< 启用异步处理 */
#endif

/** @brief 性能优化开关 */
#ifndef MCU_UART_ENABLE_ZERO_COPY
#define MCU_UART_ENABLE_ZERO_COPY       1    /**< 启用零拷贝优化 */
#endif

#ifndef MCU_UART_ENABLE_FAST_PATH
#define MCU_UART_ENABLE_FAST_PATH       1    /**< 启用快速路径优化 */
#endif

#ifndef MCU_UART_ENABLE_BATCH_PROCESSING
#define MCU_UART_ENABLE_BATCH_PROCESSING 1   /**< 启用批处理 */
#endif

/* ========== 配置参数结构 ========== */

/**
 * @brief UART硬件配置结构
 */
typedef struct mcu_uart_hw_config_s {
    const char *uart3_path;                /**< UART3设备路径 */
    const char *uart1_path;                /**< UART1设备路径 */
    __u32 uart3_baudrate;                  /**< UART3波特率 */
    __u32 uart1_baudrate;                  /**< UART1波特率 */
    __u8 data_bits;                        /**< 数据位 */
    __u8 stop_bits;                        /**< 停止位 */
    __u8 parity;                           /**< 校验位 */
    bool enable_flow_control;              /**< 启用硬件流控 */
    __u32 tx_fifo_size;                    /**< 发送FIFO大小 */
    __u32 rx_fifo_size;                    /**< 接收FIFO大小 */
    __u32 interrupt_priority;              /**< 中断优先级 */
} mcu_uart_hw_config_t;

/**
 * @brief 缓冲区配置结构
 */
typedef struct mcu_uart_buffer_config_s {
    __u32 rx_buffer_size;                  /**< 接收缓冲区大小 */
    __u32 tx_buffer_size;                  /**< 发送缓冲区大小 */
    __u32 sequel_buffer_size;              /**< 续传缓冲区大小 */
    __u32 artwork_buffer_size;             /**< 图片缓冲区大小 */
    __u32 cmd_queue_size;                  /**< 命令队列大小 */
    __u32 max_packet_length;               /**< 最大包长度 */
    __u32 buffer_alignment;                /**< 缓冲区对齐 */
    bool enable_dynamic_resize;            /**< 启用动态调整大小 */
    __u32 resize_threshold_percent;        /**< 调整大小阈值(百分比) */
    __u32 memory_pool_size;                /**< 内存池大小 */
} mcu_uart_buffer_config_t;

/**
 * @brief 协议配置结构
 */
typedef struct mcu_uart_protocol_config_s {
    bool enable_4byte_address;             /**< 启用4字节地址 */
    bool enable_ack_wait;                  /**< 启用ACK等待 */
    bool enable_cmd_number;                /**< 启用命令编号 */
    bool enable_checksum;                  /**< 启用校验和 */
    bool enable_crc;                       /**< 启用CRC校验 */
    __u32 ack_timeout_ms;                  /**< ACK超时时间(毫秒) */
    __u8 max_resend_count;                 /**< 最大重传次数 */
    __u16 device_address;                  /**< 设备地址 */
    __u8 protocol_version;                 /**< 协议版本 */
    __u32 packet_timeout_ms;               /**< 包超时时间 */
    __u32 command_timeout_ms;              /**< 命令超时时间 */
} mcu_uart_protocol_config_t;

/**
 * @brief 性能配置结构
 */
typedef struct mcu_uart_performance_config_s {
    bool enable_zero_copy;                 /**< 启用零拷贝 */
    bool enable_fast_path;                 /**< 启用快速路径 */
    bool enable_batch_processing;          /**< 启用批处理 */
    bool enable_async_processing;          /**< 启用异步处理 */
    __u32 batch_size;                      /**< 批处理大小 */
    __u32 processing_thread_priority;      /**< 处理线程优先级 */
    __u32 processing_thread_stack_size;    /**< 处理线程栈大小 */
    __u32 max_processing_time_ms;          /**< 最大处理时间 */
} mcu_uart_performance_config_t;

/**
 * @brief 调试配置结构
 */
typedef struct mcu_uart_debug_config_s {
    bool enable_recv_data_print;           /**< 打印接收数据 */
    bool enable_send_data_print;           /**< 打印发送数据 */
    bool enable_cmd_data_debug;            /**< 命令数据调试 */
    bool enable_performance_monitor;       /**< 性能监控 */
    bool enable_memory_trace;              /**< 内存跟踪 */
    bool enable_packet_trace;              /**< 数据包跟踪 */
    bool enable_error_injection;           /**< 错误注入(测试用) */
    __u32 debug_level;                     /**< 调试级别 */
    __u32 max_debug_buffer_size;           /**< 最大调试缓冲区大小 */
    const char *debug_output_path;         /**< 调试输出路径 */
} mcu_uart_debug_config_t;

/**
 * @brief 扩展配置结构
 */
typedef struct mcu_uart_extension_config_s {
    bool enable_rflink_support;            /**< 启用RFLink支持 */
    bool enable_custom_protocols;          /**< 启用自定义协议 */
    bool enable_plugin_system;             /**< 启用插件系统 */
    __u32 max_custom_handlers;             /**< 最大自定义处理器数量 */
    __u32 extension_buffer_size;           /**< 扩展缓冲区大小 */
    void *extension_context;               /**< 扩展上下文 */
} mcu_uart_extension_config_t;

/**
 * @brief 全局配置结构
 */
typedef struct mcu_uart_global_config_s {
    mcu_uart_device_type_t device_type;    /**< 设备类型 */
    mcu_uart_hw_config_t hardware;         /**< 硬件配置 */
    mcu_uart_buffer_config_t buffer;       /**< 缓冲区配置 */
    mcu_uart_protocol_config_t protocol;   /**< 协议配置 */
    mcu_uart_performance_config_t performance; /**< 性能配置 */
    mcu_uart_debug_config_t debug;         /**< 调试配置 */
    mcu_uart_extension_config_t extension; /**< 扩展配置 */
    
    /* 回调函数配置 */
    mcu_uart_data_handler_f data_handler;  /**< 数据处理回调 */
    mcu_uart_error_handler_f error_handler; /**< 错误处理回调 */
    mcu_uart_command_handler_f command_handler; /**< 命令处理回调 */
    mcu_uart_state_change_handler_f state_handler; /**< 状态变化回调 */
    void *callback_context;                /**< 回调上下文 */
    
    /* 运行时状态 */
    bool is_initialized;                   /**< 是否已初始化 */
    __u32 magic;                           /**< 魔数验证 */
    __u32 config_version;                  /**< 配置版本 */
    __u32 creation_time;                   /**< 创建时间 */
} mcu_uart_global_config_t;

/* ========== 默认配置定义 ========== */

/** @brief 默认硬件配置 */
#define MCU_UART_DEFAULT_HW_CONFIG { \
    .uart3_path = "b:\\BUS\\UART3", \
    .uart1_path = "b:\\BUS\\UART1", \
    .uart3_baudrate = 115200, \
    .uart1_baudrate = 115200, \
    .data_bits = 8, \
    .stop_bits = 1, \
    .parity = 0, \
    .enable_flow_control = false, \
    .tx_fifo_size = 64, \
    .rx_fifo_size = 64, \
    .interrupt_priority = 5 \
}

/** @brief 默认缓冲区配置 */
#define MCU_UART_DEFAULT_BUFFER_CONFIG { \
    .rx_buffer_size = MCU_UART_RX_BUFFER_SIZE, \
    .tx_buffer_size = MCU_UART_TX_BUFFER_SIZE, \
    .sequel_buffer_size = MCU_UART_SEQUEL_BUF_SIZE, \
    .artwork_buffer_size = MCU_UART_ARTWORK_BUF_SIZE, \
    .cmd_queue_size = MCU_UART_MAX_CMD_NUMBER, \
    .max_packet_length = MCU_UART_MAX_PACKET_LEN, \
    .buffer_alignment = 4, \
    .enable_dynamic_resize = true, \
    .resize_threshold_percent = 80, \
    .memory_pool_size = 32768 \
}

/** @brief 默认协议配置 */
#define MCU_UART_DEFAULT_PROTOCOL_CONFIG { \
    .enable_4byte_address = MCU_UART_ADDR_4BYTES, \
    .enable_ack_wait = MCU_UART_ACK_WAIT_ENABLED, \
    .enable_cmd_number = MCU_UART_CMD_NO_ENABLED, \
    .enable_checksum = true, \
    .enable_crc = false, \
    .ack_timeout_ms = MCU_UART_ACK_TIMEOUT_MS, \
    .max_resend_count = MCU_UART_RESEND_COUNT, \
    .device_address = MCU_UART_ALL_REMOTE_DEVICE, \
    .protocol_version = 1, \
    .packet_timeout_ms = 1000, \
    .command_timeout_ms = MCU_UART_COMMAND_TIMEOUT_MS \
}

/** @brief 默认性能配置 */
#define MCU_UART_DEFAULT_PERFORMANCE_CONFIG { \
    .enable_zero_copy = MCU_UART_ENABLE_ZERO_COPY, \
    .enable_fast_path = MCU_UART_ENABLE_FAST_PATH, \
    .enable_batch_processing = MCU_UART_ENABLE_BATCH_PROCESSING, \
    .enable_async_processing = MCU_UART_ENABLE_ASYNC_PROCESSING, \
    .batch_size = 16, \
    .processing_thread_priority = 10, \
    .processing_thread_stack_size = 8192, \
    .max_processing_time_ms = 100 \
}

/** @brief 默认调试配置 */
#define MCU_UART_DEFAULT_DEBUG_CONFIG { \
    .enable_recv_data_print = false, \
    .enable_send_data_print = false, \
    .enable_cmd_data_debug = false, \
    .enable_performance_monitor = MCU_UART_ENABLE_STATS, \
    .enable_memory_trace = false, \
    .enable_packet_trace = false, \
    .enable_error_injection = false, \
    .debug_level = 2, \
    .max_debug_buffer_size = 4096, \
    .debug_output_path = NULL \
}

/** @brief 默认扩展配置 */
#define MCU_UART_DEFAULT_EXTENSION_CONFIG { \
    .enable_rflink_support = false, \
    .enable_custom_protocols = MCU_UART_ENABLE_EXTENSIONS, \
    .enable_plugin_system = false, \
    .max_custom_handlers = 8, \
    .extension_buffer_size = 1024, \
    .extension_context = NULL \
}

/** @brief 默认全局配置 */
#define MCU_UART_DEFAULT_GLOBAL_CONFIG { \
    .device_type = MCU_UART_DEVICE_HEAD_UNIT, \
    .hardware = MCU_UART_DEFAULT_HW_CONFIG, \
    .buffer = MCU_UART_DEFAULT_BUFFER_CONFIG, \
    .protocol = MCU_UART_DEFAULT_PROTOCOL_CONFIG, \
    .performance = MCU_UART_DEFAULT_PERFORMANCE_CONFIG, \
    .debug = MCU_UART_DEFAULT_DEBUG_CONFIG, \
    .extension = MCU_UART_DEFAULT_EXTENSION_CONFIG, \
    .data_handler = NULL, \
    .error_handler = NULL, \
    .command_handler = NULL, \
    .state_handler = NULL, \
    .callback_context = NULL, \
    .is_initialized = false, \
    .magic = 0x5555AAAA, \
    .config_version = 1, \
    .creation_time = 0 \
}

/* ========== 配置管理API ========== */

/**
 * @brief 初始化配置为默认值
 * @param[out] config 配置结构指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_config_init_default(mcu_uart_global_config_t *config);

/**
 * @brief 验证配置参数有效性
 * @param[in] config 配置结构指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_config_validate(const mcu_uart_global_config_t *config);

/**
 * @brief 复制配置
 * @param[out] dst 目标配置
 * @param[in] src 源配置
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_config_copy(mcu_uart_global_config_t *dst, 
                                      const mcu_uart_global_config_t *src);

/**
 * @brief 合并配置(用src中的非默认值覆盖dst)
 * @param[in,out] dst 目标配置
 * @param[in] src 源配置
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_config_merge(mcu_uart_global_config_t *dst,
                                       const mcu_uart_global_config_t *src);

/**
 * @brief 保存配置到文件
 * @param[in] config 配置结构指针
 * @param[in] filename 文件名
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_config_save_to_file(const mcu_uart_global_config_t *config,
                                              const char *filename);

/**
 * @brief 从文件加载配置
 * @param[out] config 配置结构指针
 * @param[in] filename 文件名
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_config_load_from_file(mcu_uart_global_config_t *config,
                                                const char *filename);

/* ========== 配置设置API ========== */

/**
 * @brief 设置设备类型
 * @param[in,out] config 配置结构指针
 * @param[in] device_type 设备类型
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_config_set_device_type(
    mcu_uart_global_config_t *config,
    mcu_uart_device_type_t device_type
);

/**
 * @brief 设置UART硬件参数
 * @param[in,out] config 配置结构指针
 * @param[in] uart3_baudrate UART3波特率
 * @param[in] uart1_baudrate UART1波特率
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_config_set_uart_params(
    mcu_uart_global_config_t *config,
    __u32 uart3_baudrate,
    __u32 uart1_baudrate
);

/**
 * @brief 设置缓冲区大小
 * @param[in,out] config 配置结构指针
 * @param[in] rx_size 接收缓冲区大小
 * @param[in] tx_size 发送缓冲区大小
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_config_set_buffer_size(
    mcu_uart_global_config_t *config,
    __u32 rx_size,
    __u32 tx_size
);

/**
 * @brief 设置协议参数
 * @param[in,out] config 配置结构指针
 * @param[in] enable_4byte_addr 启用4字节地址
 * @param[in] ack_timeout_ms ACK超时时间
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_config_set_protocol_params(
    mcu_uart_global_config_t *config,
    bool enable_4byte_addr,
    __u32 ack_timeout_ms
);

/**
 * @brief 设置调试参数
 * @param[in,out] config 配置结构指针
 * @param[in] enable_debug 启用调试
 * @param[in] debug_level 调试级别
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_config_set_debug_params(
    mcu_uart_global_config_t *config,
    bool enable_debug,
    __u32 debug_level
);

/**
 * @brief 设置性能参数
 * @param[in,out] config 配置结构指针
 * @param[in] enable_zero_copy 启用零拷贝
 * @param[in] enable_async 启用异步处理
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_config_set_performance_params(
    mcu_uart_global_config_t *config,
    bool enable_zero_copy,
    bool enable_async
);

/* ========== 运行时配置查询API ========== */

/**
 * @brief 获取当前配置
 * @param[out] config 配置结构指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_config_get_current(mcu_uart_global_config_t *config);

/**
 * @brief 获取设备类型
 * @return 当前设备类型
 */
mcu_uart_device_type_t mcu_uart_config_get_device_type(void);

/**
 * @brief 获取UART3路径
 * @return UART3设备路径字符串
 */
const char* mcu_uart_config_get_uart3_path(void);

/**
 * @brief 获取UART1路径
 * @return UART1设备路径字符串
 */
const char* mcu_uart_config_get_uart1_path(void);

/**
 * @brief 检查是否启用4字节地址
 * @return true=启用, false=禁用
 */
bool mcu_uart_config_is_4byte_address_enabled(void);

/**
 * @brief 检查是否启用ACK等待
 * @return true=启用, false=禁用
 */
bool mcu_uart_config_is_ack_wait_enabled(void);

/**
 * @brief 获取ACK超时时间
 * @return ACK超时时间(毫秒)
 */
__u32 mcu_uart_config_get_ack_timeout(void);

/**
 * @brief 获取最大重传次数
 * @return 最大重传次数
 */
__u8 mcu_uart_config_get_max_resend_count(void);

/**
 * @brief 检查是否启用调试功能
 * @return true=启用, false=禁用
 */
bool mcu_uart_config_is_debug_enabled(void);

/**
 * @brief 检查是否启用性能监控
 * @return true=启用, false=禁用
 */
bool mcu_uart_config_is_performance_monitor_enabled(void);

/**
 * @brief 检查是否启用零拷贝优化
 * @return true=启用, false=禁用
 */
bool mcu_uart_config_is_zero_copy_enabled(void);

/* ========== 兼容性宏定义 ========== */

/** @brief 与原有代码的兼容性定义 */
#define is_head_unit() (mcu_uart_config_get_device_type() == MCU_UART_DEVICE_HEAD_UNIT)
#define is_wired_remote() (mcu_uart_config_get_device_type() == MCU_UART_DEVICE_WIRED_REMOTE)

#ifdef __cplusplus
}
#endif

#endif /* MCU_UART_CONFIG_H */
