/**
 * @file mcu_transport_error.c
 * @brief MCU UART传输层错误处理模块实现
 * @version 1.0
 * @date 2024-01-20
 * 
 * @copyright Copyright (c) 2024 Allwinner Technology Co., Ltd.
 * 
 * @details
 * 传输错误处理模块负责：
 * - 传输错误检测和分类
 * - 错误恢复机制
 * - 错误统计和报告
 * - 错误阈值监控
 * - 自动错误处理策略
 */

#include "mcu_uart.h"
#include "internal/mcu_transport_internal.h"
#include "internal/mcu_uart_internal.h"

#include <rtthread.h>
#include <rtdevice.h>
#include <string.h>

/* ================================ 私有宏定义 ================================ */

#define MCU_ERROR_HISTORY_SIZE             16
#define MCU_ERROR_THRESHOLD_WINDOW_MS      5000
#define MCU_ERROR_RECOVERY_TIMEOUT_MS      1000
#define MCU_ERROR_MAX_RECOVERY_ATTEMPTS    3

/* 错误分类 */
#define MCU_ERROR_CLASS_HARDWARE           0x01
#define MCU_ERROR_CLASS_PROTOCOL           0x02
#define MCU_ERROR_CLASS_TIMEOUT            0x04
#define MCU_ERROR_CLASS_BUFFER             0x08
#define MCU_ERROR_CLASS_COMMUNICATION      0x10

/* 错误严重性级别 */
#define MCU_ERROR_SEVERITY_INFO            0
#define MCU_ERROR_SEVERITY_WARNING         1
#define MCU_ERROR_SEVERITY_ERROR           2
#define MCU_ERROR_SEVERITY_CRITICAL        3
#define MCU_ERROR_SEVERITY_FATAL           4

/* ================================ 私有数据结构 ================================ */

/**
 * @brief 错误记录条目
 */
typedef struct mcu_error_record {
    mcu_uart_result_e error_code;               ///< 错误代码
    uint8_t error_class;                        ///< 错误分类
    uint8_t severity;                           ///< 严重性级别
    uint32_t timestamp;                         ///< 错误时间戳
    uint32_t module_id;                         ///< 出错模块ID
    uint32_t context_data[4];                   ///< 上下文数据
    char description[64];                       ///< 错误描述
} mcu_error_record_t;

/**
 * @brief 错误统计信息
 */
typedef struct mcu_error_statistics {
    uint32_t total_error_count;                 ///< 总错误数
    uint32_t error_count_by_class[8];           ///< 按分类统计
    uint32_t error_count_by_severity[8];        ///< 按严重性统计
    uint32_t recovery_success_count;            ///< 恢复成功次数
    uint32_t recovery_failure_count;            ///< 恢复失败次数
    uint32_t consecutive_errors;                ///< 连续错误数
    uint32_t max_consecutive_errors;            ///< 最大连续错误数
    uint32_t error_burst_count;                 ///< 错误爆发次数
    uint32_t last_error_time;                   ///< 最后错误时间
} mcu_error_statistics_t;

/**
 * @brief 错误恢复策略
 */
typedef struct mcu_error_recovery_strategy {
    mcu_uart_result_e error_code;               ///< 错误代码
    uint8_t recovery_method;                    ///< 恢复方法
    uint8_t max_attempts;                       ///< 最大尝试次数
    uint32_t retry_delay_ms;                    ///< 重试延时
    bool (*recovery_func)(void *context);       ///< 恢复函数
} mcu_error_recovery_strategy_t;

/**
 * @brief 错误处理器上下文
 */
typedef struct mcu_transport_error_context {
    mcu_uart_handle_base_t base;                ///< 基础句柄
    
    /* 错误记录 */
    mcu_error_record_t error_history[MCU_ERROR_HISTORY_SIZE]; ///< 错误历史
    uint32_t history_index;                     ///< 历史索引
    uint32_t history_count;                     ///< 历史数量
    
    /* 错误统计 */
    mcu_error_statistics_t stats;               ///< 错误统计
    
    /* 错误阈值配置 */
    uint32_t error_threshold[8];                ///< 错误阈值(按分类)
    uint32_t burst_threshold;                   ///< 爆发阈值
    uint32_t threshold_window_ms;               ///< 阈值时间窗口
    
    /* 恢复策略 */
    mcu_error_recovery_strategy_t *strategies;  ///< 恢复策略数组
    uint32_t strategy_count;                    ///< 策略数量
    
    /* 同步对象 */
    rt_mutex_t error_mutex;                     ///< 错误处理锁
    rt_timer_t monitor_timer;                   ///< 监控定时器
    
    /* 配置参数 */
    mcu_transport_config_t config;              ///< 传输配置
    
    /* 回调函数 */
    mcu_transport_error_callback_t error_callback; ///< 错误回调
    void *callback_user_data;                   ///< 回调用户数据
    
} mcu_transport_error_context_t;

/* ================================ 静态函数声明 ================================ */

static mcu_uart_result_e mcu_error_classify_error(mcu_uart_result_e error_code, 
                                                  uint8_t *error_class,
                                                  uint8_t *severity);
static mcu_uart_result_e mcu_error_add_record(mcu_transport_error_context_t *ctx,
                                              const mcu_error_record_t *record);
static mcu_uart_result_e mcu_error_check_thresholds(mcu_transport_error_context_t *ctx);
static mcu_uart_result_e mcu_error_execute_recovery(mcu_transport_error_context_t *ctx,
                                                    mcu_uart_result_e error_code);
static void mcu_error_monitor_timer_callback(void *parameter);
static bool mcu_error_recovery_reset_uart(void *context);
static bool mcu_error_recovery_flush_buffers(void *context);
static bool mcu_error_recovery_reinit_protocol(void *context);

/* 默认恢复策略 */
static mcu_error_recovery_strategy_t g_default_recovery_strategies[] = {
    {MCU_UART_ERROR_UART_TX_TIMEOUT, 1, 3, 100, mcu_error_recovery_reset_uart},
    {MCU_UART_ERROR_UART_RX_TIMEOUT, 1, 3, 100, mcu_error_recovery_reset_uart},
    {MCU_UART_ERROR_BUFFER_OVERFLOW, 2, 2, 50, mcu_error_recovery_flush_buffers},
    {MCU_UART_ERROR_CHECKSUM_MISMATCH, 3, 3, 10, mcu_error_recovery_reinit_protocol},
    {MCU_UART_ERROR_PROTOCOL_ERROR, 3, 3, 10, mcu_error_recovery_reinit_protocol},
};

/* ================================ 公共函数实现 ================================ */

/**
 * @brief 初始化传输错误处理器
 * 
 * @param handle 处理器句柄输出
 * @param config 配置参数
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_transport_error_init(mcu_transport_error_handle_t *handle,
                                          const mcu_transport_config_t *config)
{
    mcu_uart_result_e result = MCU_UART_OK;
    mcu_transport_error_context_t *ctx = NULL;
    
    /* 检查输入参数 */
    if (handle == NULL || config == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 分配上下文内存 */
    ctx = (mcu_transport_error_context_t *)rt_malloc(sizeof(mcu_transport_error_context_t));
    if (ctx == NULL) {
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    /* 初始化上下文 */
    memset(ctx, 0, sizeof(mcu_transport_error_context_t));
    memcpy(&ctx->config, config, sizeof(mcu_transport_config_t));
    
    /* 初始化基础句柄 */
    result = mcu_uart_handle_base_init(&ctx->base, MCU_UART_MODULE_TRANSPORT, ctx);
    if (result != MCU_UART_OK) {
        rt_free(ctx);
        return result;
    }
    
    /* 创建错误处理锁 */
    ctx->error_mutex = rt_mutex_create("err_hdl", RT_IPC_FLAG_FIFO);
    if (ctx->error_mutex == NULL) {
        mcu_uart_handle_base_deinit(&ctx->base);
        rt_free(ctx);
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    /* 设置默认阈值 */
    ctx->burst_threshold = 10;
    ctx->threshold_window_ms = MCU_ERROR_THRESHOLD_WINDOW_MS;
    ctx->error_threshold[0] = 50;  /* 硬件错误阈值 */
    ctx->error_threshold[1] = 30;  /* 协议错误阈值 */
    ctx->error_threshold[2] = 20;  /* 超时错误阈值 */
    ctx->error_threshold[3] = 40;  /* 缓冲区错误阈值 */
    ctx->error_threshold[4] = 25;  /* 通信错误阈值 */
    
    /* 设置默认恢复策略 */
    ctx->strategies = g_default_recovery_strategies;
    ctx->strategy_count = sizeof(g_default_recovery_strategies) / sizeof(g_default_recovery_strategies[0]);
    
    /* 创建监控定时器 */
    if (config->enable_error_monitoring) {
        ctx->monitor_timer = rt_timer_create("err_mon",
                                           mcu_error_monitor_timer_callback,
                                           ctx,
                                           rt_tick_from_millisecond(1000),
                                           RT_TIMER_FLAG_PERIODIC);
        if (ctx->monitor_timer == NULL) {
            rt_mutex_delete(ctx->error_mutex);
            mcu_uart_handle_base_deinit(&ctx->base);
            rt_free(ctx);
            return MCU_UART_ERROR_NO_MEMORY;
        }
        
        rt_timer_start(ctx->monitor_timer);
    }
    
    /* 设置输出句柄 */
    *handle = (mcu_transport_error_handle_t)ctx;
    
    return MCU_UART_OK;
}

/**
 * @brief 反初始化传输错误处理器
 * 
 * @param handle 处理器句柄
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_transport_error_deinit(mcu_transport_error_handle_t handle)
{
    mcu_transport_error_context_t *ctx = (mcu_transport_error_context_t *)handle;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_TRANSPORT);
    
    /* 停止并删除监控定时器 */
    if (ctx->monitor_timer) {
        rt_timer_stop(ctx->monitor_timer);
        rt_timer_delete(ctx->monitor_timer);
    }
    
    /* 删除错误处理锁 */
    if (ctx->error_mutex) {
        rt_mutex_delete(ctx->error_mutex);
    }
    
    /* 反初始化基础句柄 */
    mcu_uart_handle_base_deinit(&ctx->base);
    
    /* 释放内存 */
    rt_free(ctx);
    
    return MCU_UART_OK;
}

/**
 * @brief 报告传输错误
 * 
 * @param handle 处理器句柄
 * @param error_code 错误代码
 * @param module_id 模块ID
 * @param context_data 上下文数据
 * @param description 错误描述
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_transport_error_report(mcu_transport_error_handle_t handle,
                                            mcu_uart_result_e error_code,
                                            uint32_t module_id,
                                            const uint32_t *context_data,
                                            const char *description)
{
    mcu_transport_error_context_t *ctx = (mcu_transport_error_context_t *)handle;
    mcu_error_record_t record;
    mcu_uart_result_e result;
    uint8_t error_class, severity;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_TRANSPORT);
    
    /* 分类错误 */
    result = mcu_error_classify_error(error_code, &error_class, &severity);
    if (result != MCU_UART_OK) {
        return result;
    }
    
    /* 构建错误记录 */
    memset(&record, 0, sizeof(record));
    record.error_code = error_code;
    record.error_class = error_class;
    record.severity = severity;
    record.timestamp = rt_tick_get();
    record.module_id = module_id;
    
    if (context_data) {
        memcpy(record.context_data, context_data, sizeof(record.context_data));
    }
    
    if (description) {
        strncpy(record.description, description, sizeof(record.description) - 1);
    }
    
    rt_mutex_take(ctx->error_mutex, RT_WAITING_FOREVER);
    
    /* 添加错误记录 */
    result = mcu_error_add_record(ctx, &record);
    
    /* 更新统计信息 */
    ctx->stats.total_error_count++;
    ctx->stats.error_count_by_class[error_class & 0x07]++;
    ctx->stats.error_count_by_severity[severity & 0x07]++;
    ctx->stats.last_error_time = record.timestamp;
    
    /* 检查连续错误 */
    if (record.timestamp - ctx->stats.last_error_time < rt_tick_from_millisecond(1000)) {
        ctx->stats.consecutive_errors++;
        if (ctx->stats.consecutive_errors > ctx->stats.max_consecutive_errors) {
            ctx->stats.max_consecutive_errors = ctx->stats.consecutive_errors;
        }
    } else {
        ctx->stats.consecutive_errors = 1;
    }
    
    /* 检查阈值 */
    mcu_error_check_thresholds(ctx);
    
    /* 尝试错误恢复 */
    if (severity >= MCU_ERROR_SEVERITY_ERROR) {
        mcu_error_execute_recovery(ctx, error_code);
    }
    
    rt_mutex_release(ctx->error_mutex);
    
    /* 通知错误回调 */
    if (ctx->error_callback) {
        mcu_transport_error_event_t event = {
            .error_code = error_code,
            .error_class = error_class,
            .severity = severity,
            .module_id = module_id,
            .timestamp = record.timestamp,
            .description = record.description
        };
        ctx->error_callback(&event, ctx->callback_user_data);
    }
    
    return result;
}

/**
 * @brief 设置错误回调函数
 * 
 * @param handle 处理器句柄
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_transport_error_set_callback(mcu_transport_error_handle_t handle,
                                                   mcu_transport_error_callback_t callback,
                                                   void *user_data)
{
    mcu_transport_error_context_t *ctx = (mcu_transport_error_context_t *)handle;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_TRANSPORT);
    
    rt_mutex_take(ctx->error_mutex, RT_WAITING_FOREVER);
    ctx->error_callback = callback;
    ctx->callback_user_data = user_data;
    rt_mutex_release(ctx->error_mutex);
    
    return MCU_UART_OK;
}

/**
 * @brief 获取错误统计信息
 * 
 * @param handle 处理器句柄
 * @param stats 统计信息输出
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_transport_error_get_stats(mcu_transport_error_handle_t handle,
                                               mcu_transport_error_stats_t *stats)
{
    mcu_transport_error_context_t *ctx = (mcu_transport_error_context_t *)handle;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_TRANSPORT);
    
    if (stats == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    rt_mutex_take(ctx->error_mutex, RT_WAITING_FOREVER);
    
    /* 复制基础统计信息 */
    stats->total_error_count = ctx->stats.total_error_count;
    stats->recovery_success_count = ctx->stats.recovery_success_count;
    stats->recovery_failure_count = ctx->stats.recovery_failure_count;
    stats->consecutive_errors = ctx->stats.consecutive_errors;
    stats->max_consecutive_errors = ctx->stats.max_consecutive_errors;
    stats->error_burst_count = ctx->stats.error_burst_count;
    stats->last_error_time = ctx->stats.last_error_time;
    
    /* 复制分类统计 */
    memcpy(stats->error_count_by_class, ctx->stats.error_count_by_class, 
           sizeof(stats->error_count_by_class));
    memcpy(stats->error_count_by_severity, ctx->stats.error_count_by_severity,
           sizeof(stats->error_count_by_severity));
    
    rt_mutex_release(ctx->error_mutex);
    
    return MCU_UART_OK;
}

/**
 * @brief 清除错误统计信息
 * 
 * @param handle 处理器句柄
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_transport_error_clear_stats(mcu_transport_error_handle_t handle)
{
    mcu_transport_error_context_t *ctx = (mcu_transport_error_context_t *)handle;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_TRANSPORT);
    
    rt_mutex_take(ctx->error_mutex, RT_WAITING_FOREVER);
    
    /* 清除统计信息 */
    memset(&ctx->stats, 0, sizeof(ctx->stats));
    
    /* 清除历史记录 */
    memset(ctx->error_history, 0, sizeof(ctx->error_history));
    ctx->history_index = 0;
    ctx->history_count = 0;
    
    rt_mutex_release(ctx->error_mutex);
    
    return MCU_UART_OK;
}

/* ================================ 静态函数实现 ================================ */

/**
 * @brief 分类错误代码
 */
static mcu_uart_result_e mcu_error_classify_error(mcu_uart_result_e error_code, 
                                                  uint8_t *error_class,
                                                  uint8_t *severity)
{
    *error_class = 0;
    *severity = MCU_ERROR_SEVERITY_INFO;
    
    switch (error_code) {
        case MCU_UART_ERROR_UART_HARDWARE:
        case MCU_UART_ERROR_UART_CONFIG:
            *error_class = MCU_ERROR_CLASS_HARDWARE;
            *severity = MCU_ERROR_SEVERITY_CRITICAL;
            break;
            
        case MCU_UART_ERROR_UART_TX_TIMEOUT:
        case MCU_UART_ERROR_UART_RX_TIMEOUT:
            *error_class = MCU_ERROR_CLASS_TIMEOUT;
            *severity = MCU_ERROR_SEVERITY_WARNING;
            break;
            
        case MCU_UART_ERROR_BUFFER_OVERFLOW:
        case MCU_UART_ERROR_BUFFER_UNDERFLOW:
        case MCU_UART_ERROR_NO_MEMORY:
            *error_class = MCU_ERROR_CLASS_BUFFER;
            *severity = MCU_ERROR_SEVERITY_ERROR;
            break;
            
        case MCU_UART_ERROR_CHECKSUM_MISMATCH:
        case MCU_UART_ERROR_PROTOCOL_ERROR:
        case MCU_UART_ERROR_INVALID_COMMAND:
            *error_class = MCU_ERROR_CLASS_PROTOCOL;
            *severity = MCU_ERROR_SEVERITY_WARNING;
            break;
            
        case MCU_UART_ERROR_COMMUNICATION:
        case MCU_UART_ERROR_CONNECTION_LOST:
            *error_class = MCU_ERROR_CLASS_COMMUNICATION;
            *severity = MCU_ERROR_SEVERITY_ERROR;
            break;
            
        default:
            *error_class = MCU_ERROR_CLASS_COMMUNICATION;
            *severity = MCU_ERROR_SEVERITY_INFO;
            break;
    }
    
    return MCU_UART_OK;
}

/**
 * @brief 添加错误记录
 */
static mcu_uart_result_e mcu_error_add_record(mcu_transport_error_context_t *ctx,
                                              const mcu_error_record_t *record)
{
    /* 循环覆盖历史记录 */
    memcpy(&ctx->error_history[ctx->history_index], record, sizeof(mcu_error_record_t));
    
    ctx->history_index = (ctx->history_index + 1) % MCU_ERROR_HISTORY_SIZE;
    
    if (ctx->history_count < MCU_ERROR_HISTORY_SIZE) {
        ctx->history_count++;
    }
    
    return MCU_UART_OK;
}

/**
 * @brief 检查错误阈值
 */
static mcu_uart_result_e mcu_error_check_thresholds(mcu_transport_error_context_t *ctx)
{
    uint32_t current_time = rt_tick_get();
    uint32_t window_start = current_time - rt_tick_from_millisecond(ctx->threshold_window_ms);
    uint32_t class_count[8] = {0};
    uint32_t i, start_index;
    
    /* 统计时间窗口内的错误 */
    start_index = (ctx->history_index + MCU_ERROR_HISTORY_SIZE - ctx->history_count) % MCU_ERROR_HISTORY_SIZE;
    
    for (i = 0; i < ctx->history_count; i++) {
        uint32_t index = (start_index + i) % MCU_ERROR_HISTORY_SIZE;
        mcu_error_record_t *record = &ctx->error_history[index];
        
        if (record->timestamp >= window_start) {
            class_count[record->error_class & 0x07]++;
        }
    }
    
    /* 检查是否超过阈值 */
    for (i = 0; i < 8; i++) {
        if (class_count[i] > ctx->error_threshold[i]) {
            ctx->stats.error_burst_count++;
            
            /* 触发错误爆发回调 */
            if (ctx->error_callback) {
                mcu_transport_error_event_t event = {
                    .error_code = MCU_UART_ERROR_THRESHOLD_EXCEEDED,
                    .error_class = i,
                    .severity = MCU_ERROR_SEVERITY_CRITICAL,
                    .module_id = MCU_UART_MODULE_TRANSPORT,
                    .timestamp = current_time,
                    .description = "Error threshold exceeded"
                };
                ctx->error_callback(&event, ctx->callback_user_data);
            }
            break;
        }
    }
    
    return MCU_UART_OK;
}

/**
 * @brief 执行错误恢复
 */
static mcu_uart_result_e mcu_error_execute_recovery(mcu_transport_error_context_t *ctx,
                                                    mcu_uart_result_e error_code)
{
    uint32_t i;
    
    /* 查找对应的恢复策略 */
    for (i = 0; i < ctx->strategy_count; i++) {
        if (ctx->strategies[i].error_code == error_code) {
            mcu_error_recovery_strategy_t *strategy = &ctx->strategies[i];
            
            /* 执行恢复函数 */
            if (strategy->recovery_func && strategy->recovery_func(ctx)) {
                ctx->stats.recovery_success_count++;
                return MCU_UART_OK;
            } else {
                ctx->stats.recovery_failure_count++;
                return MCU_UART_ERROR_RECOVERY_FAILED;
            }
        }
    }
    
    /* 未找到对应的恢复策略 */
    return MCU_UART_ERROR_NO_RECOVERY_STRATEGY;
}

/**
 * @brief 监控定时器回调
 */
static void mcu_error_monitor_timer_callback(void *parameter)
{
    mcu_transport_error_context_t *ctx = (mcu_transport_error_context_t *)parameter;
    
    if (ctx != NULL) {
        rt_mutex_take(ctx->error_mutex, RT_WAITING_FOREVER);
        mcu_error_check_thresholds(ctx);
        rt_mutex_release(ctx->error_mutex);
    }
}

/**
 * @brief UART重置恢复函数
 */
static bool mcu_error_recovery_reset_uart(void *context)
{
    /* 实现UART重置逻辑 */
    /* 这里需要调用设备层的UART重置函数 */
    return true;
}

/**
 * @brief 缓冲区清空恢复函数
 */
static bool mcu_error_recovery_flush_buffers(void *context)
{
    /* 实现缓冲区清空逻辑 */
    /* 这里需要调用传输层的缓冲区清空函数 */
    return true;
}

/**
 * @brief 协议重新初始化恢复函数
 */
static bool mcu_error_recovery_reinit_protocol(void *context)
{
    /* 实现协议重新初始化逻辑 */
    /* 这里需要调用协议层的重新初始化函数 */
    return true;
} 