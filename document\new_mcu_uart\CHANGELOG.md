# MCU UART通信模块重构变更日志

本文档记录MCU UART通信模块重构过程中的重要变更和决策。

## [1.0.0] - 2024-01-19

### 项目启动
- **重构启动**: 开始MCU UART通信模块的全面重构
- **目标确定**: 在保持功能完全不变的前提下，使代码结构清晰、书写简洁、规则统一，并具备可扩展性

### 原有代码分析
- **模块结构分析**: 详细分析了`source/emodules/drv_mcu_uart/`的文件结构和功能
- **核心文件识别**: 
  - `mcu_interrupt_process.c` (13534行) - 核心处理逻辑
  - `mcu_interrupt_process.h` (287行) - 数据结构定义  
  - `uart_cmd_set.h` (1525行) - 命令集定义
  - `drv_mcu.c` (393行) - 驱动接口层
  - `dev_mcu.c` (752行) - 设备层接口

### 协议分析
- **发送包格式**: `0xAA55` + 长度 + 命令 + 数据 + 校验和 + `0x0D0A`
- **接收包格式**: `0x55AA` + 命令 + 长度 + 数据 + `0x0D0A`
- **命令体系**:
  - 透传命令集(0x0001-0x02FF): 系统状态、升级、USB、蓝牙等
  - 发送命令集(0x0300-0x05FF): 系统控制、音频控制等
  - 接收命令集(0x0600-0x08FF): 设备状态反馈等

### 问题识别
- **单文件过大**: 核心文件13534行，难以维护
- **职责不清**: 协议处理、设备管理、命令处理混在一起
- **全局变量多**: 状态管理混乱，线程安全问题
- **耦合度高**: 模块间依赖复杂，难以测试和扩展
- **缺乏错误处理**: 错误码不统一，错误处理不完善

### 架构设计
- **分层架构**: 设计了四层架构（应用层、协议层、传输层、设备层）
- **模块划分**: 按功能将代码分为多个独立模块
- **接口设计**: 定义了清晰的模块间接口

### 文件结构规划
- **头文件组织**: 
  - `mcu_uart.h` - 主头文件（对外接口）
  - `mcu_uart_types.h` - 基础数据类型定义
  - `mcu_uart_commands.h` - 命令定义
  - `mcu_uart_interface.h` - 接口函数定义
  - `internal/` - 内部头文件目录

- **源文件组织**:
  - `src/core/` - 核心模块
  - `src/protocol/` - 协议层
  - `src/transport/` - 传输层
  - `src/device/` - 设备层
  - `src/command/` - 命令处理层
  - `src/utils/` - 工具函数

### 协议优化决策

#### [重要] 添加包索引字段
- **背景**: 原有协议缺少包追踪机制
- **决策**: 在同步字`0xAA55`后增加1字节包索引字段`packet_index`
- **影响**: 增强了数据包的可追踪性，便于调试和故障排除
- **向后兼容**: 保持协议基本结构不变

#### [简化] 移除UART0特殊格式支持
- **背景**: 用户确认UART0特殊格式(`0xA5`开头)在实际使用中已废弃
- **决策**: 从协议设计中移除UART0特殊格式相关代码
- **影响**: 简化了协议处理逻辑，减少了代码复杂度
- **文件影响**:
  - 移除`mcu_uart_types.h`中`MCU_UART_RECV_TAG_UART0`定义
  - 移除解析状态中的UART0相关状态
  - 移除通道枚举中的`MCU_UART_CHANNEL_UART0`

#### [统一] 4字节设备地址标准化
- **背景**: 原有代码支持2字节和4字节两种设备地址（`REMOTE_ADDR_4BYTES`宏控制）
- **决策**: 用户确认现在实际使用中只保留4字节设备地址
- **影响**: 统一了设备地址格式，简化了代码逻辑
- **文件影响**:
  - 移除`mcu_protocol_header_t`中的条件编译
  - 统一使用`uint32_t device_addr`

### 数据类型定义
- **统一前缀**: 所有类型使用`mcu_uart_`或`mcu_`前缀
- **枚举规范**: 所有枚举以`_e`结尾
- **结构体规范**: 所有结构体以`_t`结尾
- **错误码统一**: 定义了统一的`mcu_uart_result_e`错误码体系

### 命令集重新组织
- **透传命令**: 保持原有定义，重新分类组织
- **发送命令**: 保持原有定义，增强可读性
- **接收命令**: 保持原有定义，完善注释

### 接口设计
- **句柄机制**: 引入句柄机制，支持多实例
- **回调机制**: 支持事件回调和命令处理器注册
- **线程安全**: 所有接口设计为线程安全
- **错误处理**: 统一的错误码返回和处理机制

### 兼容性保证
- **原有接口**: 通过宏定义保持原有接口完全兼容
- **数据结构**: 保持核心数据结构兼容
- **协议格式**: 保持协议包格式兼容
- **功能行为**: 保持所有功能行为不变

### RFLink扩展准备
- **协议抽象**: 设计了协议层抽象，支持多种协议
- **插件接口**: 预留了协议插件接口
- **配置驱动**: 支持配置文件驱动的功能控制

### 开发工具和配置
- **编译控制**: 通过宏定义控制可选功能
- **调试支持**: 内置调试日志和状态转储功能
- **性能监控**: 内置统计信息收集
- **测试支持**: 设计了单元测试和集成测试框架

## 技术决策记录

### 为什么选择分层架构？
- **职责分离**: 每层职责明确，便于维护
- **可测试性**: 每层可独立测试
- **可扩展性**: 便于增加新功能
- **可重用性**: 各层可以重用

### 为什么使用句柄机制？
- **多实例支持**: 支持创建多个模块实例
- **状态隔离**: 不同实例状态完全隔离
- **线程安全**: 便于实现线程安全
- **资源管理**: 便于资源的申请和释放

### 为什么保留原有接口？
- **渐进迁移**: 允许应用程序渐进式迁移
- **风险控制**: 降低迁移风险
- **开发效率**: 减少现有代码的修改工作量
- **测试便利**: 可以使用现有测试用例验证

### 为什么设计RFLink扩展？
- **未来需求**: 为未来可能的RF通信需求做准备
- **架构一致性**: 统一的通信架构
- **开发效率**: 复用现有的协议栈和工具
- **维护性**: 统一的维护和升级

## [1.1.0] - 2024-01-19 (当日更新)

### 新增实现
- **协议规范文档**: 完成详细的协议格式说明(`docs/04_protocol_specification.md`)
- **实现指南文档**: 提供分步实现指导(`docs/05_implementation_guide.md`)
- **环形缓冲区**: 线程安全的数据缓冲实现(`src/transport/mcu_ring_buffer.c`)
- **协议编解码器**: 完整的包编码/解码功能(`src/protocol/mcu_protocol_codec.c`)
- **设备驱动框架**: UART硬件访问基础框架(`src/device/mcu_uart_driver.c`)
- **命令处理器**: 统一的命令分发处理(`src/application/mcu_command_processor.c`)
- **内部头文件**: 传输层和协议层内部接口(`include/internal/`)
- **使用示例**: 基础使用示例代码(`examples/basic_usage.c`)

### 核心组件特性
- **环形缓冲区**:
  - 线程安全的读写操作
  - 支持阻塞和非阻塞模式
  - 内置统计信息和状态监控
  - 支持超时机制

- **协议编解码器**:
  - 完整的发送包和接收包编解码
  - 校验和计算和验证
  - ACK和错误响应包生成
  - 包格式验证和长度计算

- **设备驱动**:
  - 统一的UART硬件抽象
  - 中断驱动的数据收发
  - 多通道支持（UART1, UART3）
  - 完整的错误处理和统计

- **命令处理器**:
  - 基于命令类型的智能分发
  - 支持透传、发送、接收命令
  - 可扩展的处理函数注册机制
  - 统一的错误处理和响应生成

### 文档完善
- **协议规范**: 详细的包格式、字段定义、编解码规则
- **实现指南**: 分阶段实现步骤、开发环境配置、编译配置
- **文件结构**: 完整的项目结构和模块依赖关系
- **接口说明**: 详细的API文档和使用说明

### 代码质量
- **统一编码规范**: 遵循Melis 4.0编码标准
- **完整错误处理**: 统一的错误码体系
- **详细注释**: 中英文对照，便于理解
- **模块化设计**: 职责清晰，依赖明确

## 下一步计划

### 近期目标 (1-2周)
- [x] 完成协议规范文档
- [x] 完成实现指南文档
- [x] 开始核心模块代码实现
- [ ] 完善传输层数据收发器实现
- [ ] 完善应用层完整功能
- [ ] 编写基础单元测试

### 中期目标 (3-4周)
- [ ] 完成所有模块的代码实现
- [ ] 完成单元测试覆盖
- [ ] 完成集成测试
- [ ] 性能对比测试

### 长期目标 (1-2月)
- [ ] 完整的文档集
- [x] 示例代码和教程
- [ ] RFLink扩展实现
- [ ] 性能优化和调优

## 风险和挑战

### 技术风险
- **性能影响**: 分层架构可能带来性能开销
- **内存使用**: 新架构可能增加内存使用
- **兼容性问题**: 可能存在未发现的兼容性问题

### 应对措施
- **性能测试**: 严格的性能对比测试
- **内存监控**: 实时内存使用监控
- **回归测试**: 完善的回归测试套件

### 项目风险
- **时间压力**: 重构工作量较大
- **资源限制**: 开发和测试资源有限
- **变更管理**: 需要协调多方面的变更

### 应对策略
- **分阶段交付**: 按模块分阶段交付和验证
- **增量迁移**: 支持增量迁移和回滚
- **充分测试**: 多轮测试验证确保质量

## 总结

MCU UART通信模块重构项目已经完成了前期的分析、设计和规划工作。新的架构设计在保持完全兼容的前提下，大幅提升了代码的可维护性、可扩展性和可测试性，为后续的功能扩展（特别是RFLink）奠定了良好的基础。

接下来将进入具体的代码实现阶段，将严格按照设计文档进行开发，确保重构目标的达成。 