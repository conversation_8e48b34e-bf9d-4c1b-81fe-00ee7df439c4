/**
 * @file mcu_packet_manager.c
 * @brief MCU UART协议数据包管理器实现
 * @version 1.0
 * @date 2024-01-20
 * 
 * @copyright Copyright (c) 2024 Allwinner Technology Co., Ltd.
 * 
 * @details
 * 数据包管理器负责：
 * - 数据包队列管理
 * - 发送队列调度
 * - 接收包分发
 * - 包序号管理
 * - 包重复检测
 */

#include "mcu_uart.h"
#include "internal/mcu_protocol_internal.h"
#include "internal/mcu_uart_internal.h"

#include <rtthread.h>
#include <rtdevice.h>
#include <string.h>

/* ================================ 私有宏定义 ================================ */

#define MCU_PACKET_QUEUE_MAX_SIZE          64
#define MCU_PACKET_HISTORY_SIZE            16
#define MCU_PACKET_MAX_RETRY_COUNT         3
#define MCU_PACKET_TIMEOUT_MS              1000
#define MCU_PACKET_SEQUENCE_MASK           0xFF

/* 队列类型 */
#define MCU_QUEUE_TYPE_TX                  0
#define MCU_QUEUE_TYPE_RX                  1
#define MCU_QUEUE_TYPE_PENDING             2

/* ================================ 私有数据结构 ================================ */

/**
 * @brief 数据包队列节点
 */
typedef struct mcu_packet_node {
    struct mcu_packet_node *next;               ///< 下一个节点
    mcu_protocol_packet_t packet;               ///< 数据包
    uint32_t timestamp;                         ///< 时间戳
    uint8_t retry_count;                        ///< 重试计数
    bool need_ack;                              ///< 是否需要ACK
} mcu_packet_node_t;

/**
 * @brief 数据包队列
 */
typedef struct mcu_packet_queue {
    mcu_packet_node_t *head;                    ///< 队列头
    mcu_packet_node_t *tail;                    ///< 队列尾
    uint32_t count;                             ///< 节点数量
    uint32_t max_size;                          ///< 最大大小
    rt_sem_t sem;                               ///< 信号量
    rt_mutex_t mutex;                           ///< 互斥锁
} mcu_packet_queue_t;

/**
 * @brief 包序号管理器
 */
typedef struct mcu_sequence_manager {
    uint8_t next_tx_seq;                        ///< 下一个发送序号
    uint8_t last_rx_seq;                        ///< 最后接收序号
    uint8_t rx_history[MCU_PACKET_HISTORY_SIZE]; ///< 接收历史
    uint8_t history_index;                      ///< 历史索引
    rt_mutex_t mutex;                           ///< 序号保护锁
} mcu_sequence_manager_t;

/**
 * @brief 数据包管理器上下文
 */
typedef struct mcu_packet_manager_context {
    mcu_uart_handle_base_t base;                ///< 基础句柄
    
    /* 数据包队列 */
    mcu_packet_queue_t tx_queue;                ///< 发送队列
    mcu_packet_queue_t rx_queue;                ///< 接收队列
    mcu_packet_queue_t pending_queue;           ///< 待确认队列
    
    /* 序号管理 */
    mcu_sequence_manager_t seq_mgr;             ///< 序号管理器
    
    /* 配置参数 */
    mcu_protocol_config_t config;               ///< 协议配置
    
    /* 统计信息 */
    mcu_protocol_packet_stats_t stats;          ///< 包统计信息
    
    /* 回调函数 */
    mcu_protocol_packet_callback_t packet_callback; ///< 包处理回调
    void *callback_user_data;                   ///< 回调用户数据
    
} mcu_packet_manager_context_t;

/* ================================ 静态函数声明 ================================ */

static mcu_uart_result_e mcu_packet_queue_init(mcu_packet_queue_t *queue, 
                                               uint32_t max_size, 
                                               const char *name);
static void mcu_packet_queue_deinit(mcu_packet_queue_t *queue);
static mcu_uart_result_e mcu_packet_queue_push(mcu_packet_queue_t *queue, 
                                               const mcu_protocol_packet_t *packet,
                                               bool need_ack);
static mcu_uart_result_e mcu_packet_queue_pop(mcu_packet_queue_t *queue, 
                                              mcu_packet_node_t **node);
static mcu_uart_result_e mcu_packet_queue_peek(mcu_packet_queue_t *queue,
                                               mcu_packet_node_t **node);
static void mcu_packet_node_free(mcu_packet_node_t *node);
static mcu_packet_node_t *mcu_packet_node_create(const mcu_protocol_packet_t *packet, 
                                                 bool need_ack);

static mcu_uart_result_e mcu_sequence_manager_init(mcu_sequence_manager_t *seq_mgr);
static void mcu_sequence_manager_deinit(mcu_sequence_manager_t *seq_mgr);
static uint8_t mcu_sequence_manager_get_next_tx_seq(mcu_sequence_manager_t *seq_mgr);
static bool mcu_sequence_manager_check_rx_seq(mcu_sequence_manager_t *seq_mgr, 
                                              uint8_t seq);
static void mcu_sequence_manager_update_rx_seq(mcu_sequence_manager_t *seq_mgr, 
                                               uint8_t seq);

static mcu_uart_result_e mcu_packet_manager_process_timeouts(mcu_packet_manager_context_t *ctx);
static mcu_uart_result_e mcu_packet_manager_handle_ack(mcu_packet_manager_context_t *ctx,
                                                       const mcu_protocol_packet_t *ack_packet);

/* ================================ 公共函数实现 ================================ */

/**
 * @brief 初始化数据包管理器
 * 
 * @param handle 管理器句柄输出
 * @param config 配置参数
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_packet_manager_init(mcu_packet_manager_handle_t *handle,
                                         const mcu_protocol_config_t *config)
{
    mcu_uart_result_e result = MCU_UART_OK;
    mcu_packet_manager_context_t *ctx = NULL;
    
    /* 检查输入参数 */
    if (handle == NULL || config == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 分配上下文内存 */
    ctx = (mcu_packet_manager_context_t *)rt_malloc(sizeof(mcu_packet_manager_context_t));
    if (ctx == NULL) {
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    /* 初始化上下文 */
    memset(ctx, 0, sizeof(mcu_packet_manager_context_t));
    memcpy(&ctx->config, config, sizeof(mcu_protocol_config_t));
    
    /* 初始化基础句柄 */
    result = mcu_uart_handle_base_init(&ctx->base, MCU_UART_MODULE_PROTOCOL, ctx);
    if (result != MCU_UART_OK) {
        rt_free(ctx);
        return result;
    }
    
    /* 初始化发送队列 */
    result = mcu_packet_queue_init(&ctx->tx_queue, MCU_PACKET_QUEUE_MAX_SIZE, "tx_queue");
    if (result != MCU_UART_OK) {
        mcu_uart_handle_base_deinit(&ctx->base);
        rt_free(ctx);
        return result;
    }
    
    /* 初始化接收队列 */
    result = mcu_packet_queue_init(&ctx->rx_queue, MCU_PACKET_QUEUE_MAX_SIZE, "rx_queue");
    if (result != MCU_UART_OK) {
        mcu_packet_queue_deinit(&ctx->tx_queue);
        mcu_uart_handle_base_deinit(&ctx->base);
        rt_free(ctx);
        return result;
    }
    
    /* 初始化待确认队列 */
    result = mcu_packet_queue_init(&ctx->pending_queue, MCU_PACKET_QUEUE_MAX_SIZE, "pending_queue");
    if (result != MCU_UART_OK) {
        mcu_packet_queue_deinit(&ctx->rx_queue);
        mcu_packet_queue_deinit(&ctx->tx_queue);
        mcu_uart_handle_base_deinit(&ctx->base);
        rt_free(ctx);
        return result;
    }
    
    /* 初始化序号管理器 */
    result = mcu_sequence_manager_init(&ctx->seq_mgr);
    if (result != MCU_UART_OK) {
        mcu_packet_queue_deinit(&ctx->pending_queue);
        mcu_packet_queue_deinit(&ctx->rx_queue);
        mcu_packet_queue_deinit(&ctx->tx_queue);
        mcu_uart_handle_base_deinit(&ctx->base);
        rt_free(ctx);
        return result;
    }
    
    /* 设置输出句柄 */
    *handle = (mcu_packet_manager_handle_t)ctx;
    
    return MCU_UART_OK;
}

/**
 * @brief 反初始化数据包管理器
 * 
 * @param handle 管理器句柄
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_packet_manager_deinit(mcu_packet_manager_handle_t handle)
{
    mcu_packet_manager_context_t *ctx = (mcu_packet_manager_context_t *)handle;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_PROTOCOL);
    
    /* 反初始化序号管理器 */
    mcu_sequence_manager_deinit(&ctx->seq_mgr);
    
    /* 反初始化队列 */
    mcu_packet_queue_deinit(&ctx->pending_queue);
    mcu_packet_queue_deinit(&ctx->rx_queue);
    mcu_packet_queue_deinit(&ctx->tx_queue);
    
    /* 反初始化基础句柄 */
    mcu_uart_handle_base_deinit(&ctx->base);
    
    /* 释放内存 */
    rt_free(ctx);
    
    return MCU_UART_OK;
}

/**
 * @brief 添加待发送数据包
 * 
 * @param handle 管理器句柄
 * @param packet 数据包
 * @param need_ack 是否需要ACK
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_packet_manager_send_packet(mcu_packet_manager_handle_t handle,
                                                 const mcu_protocol_packet_t *packet,
                                                 bool need_ack)
{
    mcu_packet_manager_context_t *ctx = (mcu_packet_manager_context_t *)handle;
    mcu_uart_result_e result;
    mcu_protocol_packet_t tx_packet;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_PROTOCOL);
    
    if (packet == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    MCU_UART_LOCK_MODULE(ctx);
    
    /* 复制数据包并分配序号 */
    memcpy(&tx_packet, packet, sizeof(mcu_protocol_packet_t));
    tx_packet.header.frame_number = mcu_sequence_manager_get_next_tx_seq(&ctx->seq_mgr);
    
    /* 添加到发送队列 */
    result = mcu_packet_queue_push(&ctx->tx_queue, &tx_packet, need_ack);
    if (result == MCU_UART_OK) {
        ctx->stats.tx_queue_count++;
    }
    
    MCU_UART_UNLOCK_MODULE(ctx);
    
    return result;
}

/**
 * @brief 获取下一个待发送数据包
 * 
 * @param handle 管理器句柄
 * @param packet 数据包输出
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_packet_manager_get_next_tx_packet(mcu_packet_manager_handle_t handle,
                                                       mcu_protocol_packet_t *packet)
{
    mcu_packet_manager_context_t *ctx = (mcu_packet_manager_context_t *)handle;
    mcu_uart_result_e result;
    mcu_packet_node_t *node = NULL;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_PROTOCOL);
    
    if (packet == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    MCU_UART_LOCK_MODULE(ctx);
    
    /* 从发送队列获取数据包 */
    result = mcu_packet_queue_pop(&ctx->tx_queue, &node);
    if (result == MCU_UART_OK && node != NULL) {
        /* 复制数据包 */
        memcpy(packet, &node->packet, sizeof(mcu_protocol_packet_t));
        
        /* 如果需要ACK，添加到待确认队列 */
        if (node->need_ack) {
            mcu_packet_queue_push(&ctx->pending_queue, &node->packet, true);
        }
        
        /* 释放节点 */
        mcu_packet_node_free(node);
        
        ctx->stats.tx_packet_count++;
    }
    
    MCU_UART_UNLOCK_MODULE(ctx);
    
    return result;
}

/**
 * @brief 处理接收到的数据包
 * 
 * @param handle 管理器句柄
 * @param packet 接收的数据包
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_packet_manager_receive_packet(mcu_packet_manager_handle_t handle,
                                                   const mcu_protocol_packet_t *packet)
{
    mcu_packet_manager_context_t *ctx = (mcu_packet_manager_context_t *)handle;
    mcu_uart_result_e result = MCU_UART_OK;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_PROTOCOL);
    
    if (packet == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    MCU_UART_LOCK_MODULE(ctx);
    
    /* 检查是否是ACK包 */
    if (packet->header.ack_flag) {
        /* 处理ACK */
        result = mcu_packet_manager_handle_ack(ctx, packet);
    } else {
        /* 检查包序号 */
        if (mcu_sequence_manager_check_rx_seq(&ctx->seq_mgr, packet->header.frame_number)) {
            /* 更新接收序号 */
            mcu_sequence_manager_update_rx_seq(&ctx->seq_mgr, packet->header.frame_number);
            
            /* 添加到接收队列 */
            result = mcu_packet_queue_push(&ctx->rx_queue, packet, false);
            if (result == MCU_UART_OK) {
                ctx->stats.rx_packet_count++;
            }
        } else {
            /* 重复包，丢弃 */
            ctx->stats.duplicate_packet_count++;
            result = MCU_UART_ERROR_DUPLICATE_PACKET;
        }
    }
    
    MCU_UART_UNLOCK_MODULE(ctx);
    
    return result;
}

/**
 * @brief 获取下一个接收数据包
 * 
 * @param handle 管理器句柄
 * @param packet 数据包输出
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_packet_manager_get_next_rx_packet(mcu_packet_manager_handle_t handle,
                                                       mcu_protocol_packet_t *packet)
{
    mcu_packet_manager_context_t *ctx = (mcu_packet_manager_context_t *)handle;
    mcu_uart_result_e result;
    mcu_packet_node_t *node = NULL;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_PROTOCOL);
    
    if (packet == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    MCU_UART_LOCK_MODULE(ctx);
    
    /* 从接收队列获取数据包 */
    result = mcu_packet_queue_pop(&ctx->rx_queue, &node);
    if (result == MCU_UART_OK && node != NULL) {
        /* 复制数据包 */
        memcpy(packet, &node->packet, sizeof(mcu_protocol_packet_t));
        
        /* 释放节点 */
        mcu_packet_node_free(node);
        
        ctx->stats.rx_queue_count++;
    }
    
    MCU_UART_UNLOCK_MODULE(ctx);
    
    return result;
}

/**
 * @brief 处理超时和重传
 * 
 * @param handle 管理器句柄
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_packet_manager_process_timeouts(mcu_packet_manager_handle_t handle)
{
    mcu_packet_manager_context_t *ctx = (mcu_packet_manager_context_t *)handle;
    mcu_uart_result_e result;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_PROTOCOL);
    
    MCU_UART_LOCK_MODULE(ctx);
    result = mcu_packet_manager_process_timeouts(ctx);
    MCU_UART_UNLOCK_MODULE(ctx);
    
    return result;
}

/**
 * @brief 获取包统计信息
 * 
 * @param handle 管理器句柄
 * @param stats 统计信息输出
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_packet_manager_get_stats(mcu_packet_manager_handle_t handle,
                                               mcu_protocol_packet_stats_t *stats)
{
    mcu_packet_manager_context_t *ctx = (mcu_packet_manager_context_t *)handle;
    
    /* 验证句柄 */
    MCU_UART_VALIDATE_HANDLE(ctx, MCU_UART_MODULE_PROTOCOL);
    
    if (stats == NULL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    MCU_UART_LOCK_MODULE(ctx);
    memcpy(stats, &ctx->stats, sizeof(mcu_protocol_packet_stats_t));
    MCU_UART_UNLOCK_MODULE(ctx);
    
    return MCU_UART_OK;
}

/* ================================ 静态函数实现 ================================ */

/**
 * @brief 初始化数据包队列
 */
static mcu_uart_result_e mcu_packet_queue_init(mcu_packet_queue_t *queue, 
                                               uint32_t max_size, 
                                               const char *name)
{
    char sem_name[32], mutex_name[32];
    
    memset(queue, 0, sizeof(mcu_packet_queue_t));
    queue->max_size = max_size;
    
    /* 创建信号量 */
    snprintf(sem_name, sizeof(sem_name), "pkt_%s_sem", name);
    queue->sem = rt_sem_create(sem_name, 0, RT_IPC_FLAG_FIFO);
    if (queue->sem == NULL) {
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    /* 创建互斥锁 */
    snprintf(mutex_name, sizeof(mutex_name), "pkt_%s_mtx", name);
    queue->mutex = rt_mutex_create(mutex_name, RT_IPC_FLAG_FIFO);
    if (queue->mutex == NULL) {
        rt_sem_delete(queue->sem);
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    return MCU_UART_OK;
}

/**
 * @brief 反初始化数据包队列
 */
static void mcu_packet_queue_deinit(mcu_packet_queue_t *queue)
{
    mcu_packet_node_t *node, *next;
    
    if (queue == NULL) return;
    
    /* 释放所有节点 */
    node = queue->head;
    while (node != NULL) {
        next = node->next;
        mcu_packet_node_free(node);
        node = next;
    }
    
    /* 删除同步对象 */
    if (queue->sem) {
        rt_sem_delete(queue->sem);
    }
    if (queue->mutex) {
        rt_mutex_delete(queue->mutex);
    }
    
    memset(queue, 0, sizeof(mcu_packet_queue_t));
}

/**
 * @brief 向队列添加数据包
 */
static mcu_uart_result_e mcu_packet_queue_push(mcu_packet_queue_t *queue, 
                                               const mcu_protocol_packet_t *packet,
                                               bool need_ack)
{
    mcu_packet_node_t *node;
    
    /* 检查队列是否已满 */
    if (queue->count >= queue->max_size) {
        return MCU_UART_ERROR_QUEUE_FULL;
    }
    
    /* 创建新节点 */
    node = mcu_packet_node_create(packet, need_ack);
    if (node == NULL) {
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    rt_mutex_take(queue->mutex, RT_WAITING_FOREVER);
    
    /* 添加到队列尾部 */
    if (queue->tail == NULL) {
        queue->head = queue->tail = node;
    } else {
        queue->tail->next = node;
        queue->tail = node;
    }
    queue->count++;
    
    rt_mutex_release(queue->mutex);
    
    /* 释放信号量 */
    rt_sem_release(queue->sem);
    
    return MCU_UART_OK;
}

/**
 * @brief 从队列获取数据包
 */
static mcu_uart_result_e mcu_packet_queue_pop(mcu_packet_queue_t *queue, 
                                              mcu_packet_node_t **node)
{
    /* 等待信号量 */
    if (rt_sem_take(queue->sem, 0) != RT_EOK) {
        return MCU_UART_ERROR_NO_DATA;
    }
    
    rt_mutex_take(queue->mutex, RT_WAITING_FOREVER);
    
    /* 从队列头部取出 */
    *node = queue->head;
    if (*node != NULL) {
        queue->head = (*node)->next;
        if (queue->head == NULL) {
            queue->tail = NULL;
        }
        queue->count--;
        (*node)->next = NULL;
    }
    
    rt_mutex_release(queue->mutex);
    
    return (*node != NULL) ? MCU_UART_OK : MCU_UART_ERROR_NO_DATA;
}

/**
 * @brief 创建数据包节点
 */
static mcu_packet_node_t *mcu_packet_node_create(const mcu_protocol_packet_t *packet, 
                                                 bool need_ack)
{
    mcu_packet_node_t *node;
    
    node = (mcu_packet_node_t *)rt_malloc(sizeof(mcu_packet_node_t));
    if (node == NULL) {
        return NULL;
    }
    
    memset(node, 0, sizeof(mcu_packet_node_t));
    memcpy(&node->packet, packet, sizeof(mcu_protocol_packet_t));
    node->timestamp = rt_tick_get();
    node->need_ack = need_ack;
    node->retry_count = 0;
    
    return node;
}

/**
 * @brief 释放数据包节点
 */
static void mcu_packet_node_free(mcu_packet_node_t *node)
{
    if (node != NULL) {
        rt_free(node);
    }
}

/**
 * @brief 初始化序号管理器
 */
static mcu_uart_result_e mcu_sequence_manager_init(mcu_sequence_manager_t *seq_mgr)
{
    memset(seq_mgr, 0, sizeof(mcu_sequence_manager_t));
    
    seq_mgr->next_tx_seq = 1;
    seq_mgr->last_rx_seq = 0;
    
    seq_mgr->mutex = rt_mutex_create("seq_mgr", RT_IPC_FLAG_FIFO);
    if (seq_mgr->mutex == NULL) {
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    return MCU_UART_OK;
}

/**
 * @brief 反初始化序号管理器
 */
static void mcu_sequence_manager_deinit(mcu_sequence_manager_t *seq_mgr)
{
    if (seq_mgr->mutex) {
        rt_mutex_delete(seq_mgr->mutex);
        seq_mgr->mutex = NULL;
    }
}

/**
 * @brief 获取下一个发送序号
 */
static uint8_t mcu_sequence_manager_get_next_tx_seq(mcu_sequence_manager_t *seq_mgr)
{
    uint8_t seq;
    
    rt_mutex_take(seq_mgr->mutex, RT_WAITING_FOREVER);
    seq = seq_mgr->next_tx_seq;
    seq_mgr->next_tx_seq = (seq_mgr->next_tx_seq + 1) & MCU_PACKET_SEQUENCE_MASK;
    if (seq_mgr->next_tx_seq == 0) {
        seq_mgr->next_tx_seq = 1; /* 避免序号0 */
    }
    rt_mutex_release(seq_mgr->mutex);
    
    return seq;
}

/**
 * @brief 检查接收序号是否有效
 */
static bool mcu_sequence_manager_check_rx_seq(mcu_sequence_manager_t *seq_mgr, 
                                              uint8_t seq)
{
    bool is_valid = true;
    int i;
    
    rt_mutex_take(seq_mgr->mutex, RT_WAITING_FOREVER);
    
    /* 检查是否在历史中 */
    for (i = 0; i < MCU_PACKET_HISTORY_SIZE; i++) {
        if (seq_mgr->rx_history[i] == seq) {
            is_valid = false;
            break;
        }
    }
    
    rt_mutex_release(seq_mgr->mutex);
    
    return is_valid;
}

/**
 * @brief 更新接收序号
 */
static void mcu_sequence_manager_update_rx_seq(mcu_sequence_manager_t *seq_mgr, 
                                               uint8_t seq)
{
    rt_mutex_take(seq_mgr->mutex, RT_WAITING_FOREVER);
    
    seq_mgr->last_rx_seq = seq;
    seq_mgr->rx_history[seq_mgr->history_index] = seq;
    seq_mgr->history_index = (seq_mgr->history_index + 1) % MCU_PACKET_HISTORY_SIZE;
    
    rt_mutex_release(seq_mgr->mutex);
}

/**
 * @brief 处理ACK数据包
 */
static mcu_uart_result_e mcu_packet_manager_handle_ack(mcu_packet_manager_context_t *ctx,
                                                       const mcu_protocol_packet_t *ack_packet)
{
    mcu_packet_node_t *node, *prev, *next;
    uint8_t ack_seq = ack_packet->header.frame_number;
    bool found = false;
    
    /* 在待确认队列中查找对应的包 */
    prev = NULL;
    node = ctx->pending_queue.head;
    
    while (node != NULL) {
        next = node->next;
        
        if (node->packet.header.frame_number == ack_seq) {
            /* 找到对应的包，从队列中移除 */
            if (prev == NULL) {
                ctx->pending_queue.head = next;
            } else {
                prev->next = next;
            }
            
            if (next == NULL) {
                ctx->pending_queue.tail = prev;
            }
            
            ctx->pending_queue.count--;
            mcu_packet_node_free(node);
            found = true;
            ctx->stats.ack_received_count++;
            break;
        }
        
        prev = node;
        node = next;
    }
    
    return found ? MCU_UART_OK : MCU_UART_ERROR_NOT_FOUND;
}

/**
 * @brief 处理超时包
 */
static mcu_uart_result_e mcu_packet_manager_process_timeouts(mcu_packet_manager_context_t *ctx)
{
    mcu_packet_node_t *node, *prev, *next;
    uint32_t current_time = rt_tick_get();
    uint32_t timeout_ticks = rt_tick_from_millisecond(MCU_PACKET_TIMEOUT_MS);
    
    prev = NULL;
    node = ctx->pending_queue.head;
    
    while (node != NULL) {
        next = node->next;
        
        /* 检查是否超时 */
        if ((current_time - node->timestamp) > timeout_ticks) {
            if (node->retry_count < MCU_PACKET_MAX_RETRY_COUNT) {
                /* 重新发送 */
                node->retry_count++;
                node->timestamp = current_time;
                mcu_packet_queue_push(&ctx->tx_queue, &node->packet, true);
                ctx->stats.retry_count++;
            } else {
                /* 超过重试次数，移除 */
                if (prev == NULL) {
                    ctx->pending_queue.head = next;
                } else {
                    prev->next = next;
                }
                
                if (next == NULL) {
                    ctx->pending_queue.tail = prev;
                }
                
                ctx->pending_queue.count--;
                mcu_packet_node_free(node);
                ctx->stats.timeout_count++;
            }
        }
        
        prev = node;
        node = next;
    }
    
    return MCU_UART_OK;
} 