/**
 * @file mcu_command_processor.c
 * @brief 命令处理器实现
 * 
 * 实现MCU UART通信的命令处理逻辑，包括命令解析、执行、响应等。
 * 支持透传命令、发送命令和接收命令的统一处理。
 * 
 * <AUTHOR> UART Team
 * @date 2025-01-XX
 */

#include "mcu_uart_types.h"
#include "mcu_uart_commands.h"
#include "internal/mcu_application_internal.h"
#include <rtthread.h>
#include <string.h>

/* ========================================================================== */
/*                             本地变量                                       */
/* ========================================================================== */

/** 命令处理器上下文 */
static mcu_command_processor_t g_command_processor;

/** 命令处理器初始化标志 */
static bool g_processor_initialized = false;

/** 命令处理器互斥锁 */
static rt_mutex_t g_processor_mutex = NULL;

/** 命令回调函数表 */
static mcu_command_handler_t g_command_handlers[MCU_CMD_MAX_HANDLERS];
static uint32_t g_handler_count = 0;

/* ========================================================================== */
/*                           内部函数声明                                     */
/* ========================================================================== */

static mcu_uart_result_e mcu_command_dispatch(const mcu_protocol_packet_t *packet, 
                                               mcu_protocol_packet_t *response);
static mcu_uart_result_e mcu_command_process_transparent(const mcu_protocol_packet_t *packet, 
                                                          mcu_protocol_packet_t *response);
static mcu_uart_result_e mcu_command_process_send(const mcu_protocol_packet_t *packet, 
                                                   mcu_protocol_packet_t *response);
static mcu_uart_result_e mcu_command_process_receive(const mcu_protocol_packet_t *packet, 
                                                      mcu_protocol_packet_t *response);
static bool mcu_command_is_transparent(uint16_t command);
static bool mcu_command_is_send(uint16_t command);
static bool mcu_command_is_receive(uint16_t command);

/* ========================================================================== */
/*                           初始化和清理                                     */
/* ========================================================================== */

/**
 * @brief 初始化命令处理器
 * 
 * @return mcu_uart_result_e 初始化结果
 */
mcu_uart_result_e mcu_command_processor_init(void)
{
    if (g_processor_initialized) {
        return MCU_UART_OK;
    }
    
    // 创建互斥锁
    g_processor_mutex = rt_mutex_create("cmd_proc_mtx", RT_IPC_FLAG_FIFO);
    if (!g_processor_mutex) {
        return MCU_UART_NO_MEMORY;
    }
    
    g_processor_initialized = true;
    return MCU_UART_OK;
}

/**
 * @brief 反初始化命令处理器
 * 
 * @return mcu_uart_result_e 操作结果
 */
mcu_uart_result_e mcu_command_processor_deinit(void)
{
    if (!g_processor_initialized) {
        return MCU_UART_OK;
    }
    
    if (g_processor_mutex) {
        rt_mutex_delete(g_processor_mutex);
        g_processor_mutex = NULL;
    }
    
    g_processor_initialized = false;
    return MCU_UART_OK;
}

/* ========================================================================== */
/*                           命令处理接口                                     */
/* ========================================================================== */

/**
 * @brief 处理命令包
 * 
 * @param packet 输入命令包
 * @param response 输出响应包（可选）
 * @return mcu_uart_result_e 处理结果
 */
mcu_uart_result_e mcu_command_processor_process_packet(const mcu_protocol_packet_t *packet,
                                                        mcu_protocol_packet_t *response)
{
    if (!g_processor_initialized || !packet) {
        return MCU_UART_INVALID_PARAM;
    }
    
    rt_mutex_take(g_processor_mutex, RT_WAITING_FOREVER);
    
    // 基础命令处理逻辑
    mcu_uart_result_e result = MCU_UART_OK;
    uint16_t command = packet->command;
    
    // 根据命令类型进行处理
    if (command >= MCU_CMD_TRANSPARENT_START && command <= MCU_CMD_TRANSPARENT_END) {
        // 透传命令处理
        if (response) {
            memcpy(response, packet, sizeof(mcu_protocol_packet_t));
            response->packet_type = MCU_PACKET_TYPE_RECV;
        }
    } else if (command >= MCU_CMD_SEND_START && command <= MCU_CMD_SEND_END) {
        // 发送命令处理
        if (response) {
            response->packet_type = MCU_PACKET_TYPE_RECV;
            response->command = packet->command | 0x8000; // ACK标志
            response->data_length = 2;
        }
    } else if (command >= MCU_CMD_RECEIVE_START && command <= MCU_CMD_RECEIVE_END) {
        // 接收命令处理
        result = MCU_UART_OK;
    } else {
        result = MCU_UART_UNSUPPORTED_COMMAND;
    }
    
    rt_mutex_release(g_processor_mutex);
    
    return result;
}

/**
 * @brief 异步处理命令包
 * 
 * @param packet 命令包
 * @param timeout_ms 超时时间
 * @return mcu_uart_result_e 操作结果
 */
mcu_uart_result_e mcu_command_processor_process_async(const mcu_protocol_packet_t *packet,
                                                       uint32_t timeout_ms)
{
    if (!g_processor_initialized || !packet) {
        return MCU_UART_INVALID_PARAM;
    }
    
    mcu_command_item_t item;
    memcpy(&item.packet, packet, sizeof(mcu_protocol_packet_t));
    item.timestamp = rt_tick_get();
    item.timeout_ms = timeout_ms;
    
    rt_tick_t timeout_ticks = (timeout_ms == RT_WAITING_FOREVER) ? 
                              RT_WAITING_FOREVER : rt_tick_from_millisecond(timeout_ms);
    
    if (rt_mq_send(g_command_processor.command_queue, &item, sizeof(item)) != RT_EOK) {
        g_command_processor.stats.queue_full_errors++;
        return MCU_UART_QUEUE_FULL;
    }
    
    return MCU_UART_OK;
}

/**
 * @brief 获取响应包
 * 
 * @param response 输出响应包
 * @param timeout_ms 超时时间
 * @return mcu_uart_result_e 操作结果
 */
mcu_uart_result_e mcu_command_processor_get_response(mcu_protocol_packet_t *response,
                                                      uint32_t timeout_ms)
{
    if (!g_processor_initialized || !response) {
        return MCU_UART_INVALID_PARAM;
    }
    
    mcu_response_item_t item;
    rt_tick_t timeout_ticks = (timeout_ms == RT_WAITING_FOREVER) ? 
                              RT_WAITING_FOREVER : rt_tick_from_millisecond(timeout_ms);
    
    if (rt_mq_recv(g_command_processor.response_queue, &item, sizeof(item), timeout_ticks) != RT_EOK) {
        return MCU_UART_TIMEOUT;
    }
    
    memcpy(response, &item.packet, sizeof(mcu_protocol_packet_t));
    return MCU_UART_OK;
}

/* ========================================================================== */
/*                         命令处理器注册                                     */
/* ========================================================================== */

/**
 * @brief 注册命令处理函数
 * 
 * @param command_start 命令起始范围
 * @param command_end 命令结束范围
 * @param handler 处理函数
 * @param user_data 用户数据
 * @return mcu_uart_result_e 注册结果
 */
mcu_uart_result_e mcu_command_processor_register_handler(uint16_t command_start,
                                                          uint16_t command_end,
                                                          mcu_command_callback_t handler,
                                                          void *user_data)
{
    if (!g_processor_initialized || !handler || command_start > command_end) {
        return MCU_UART_INVALID_PARAM;
    }
    
    rt_mutex_take(g_processor_mutex, RT_WAITING_FOREVER);
    
    if (g_handler_count >= MCU_CMD_MAX_HANDLERS) {
        rt_mutex_release(g_processor_mutex);
        return MCU_UART_NO_MEMORY;
    }
    
    // 检查命令范围是否重叠
    for (uint32_t i = 0; i < g_handler_count; i++) {
        if ((command_start <= g_command_handlers[i].command_end) &&
            (command_end >= g_command_handlers[i].command_start)) {
            rt_mutex_release(g_processor_mutex);
            return MCU_UART_ALREADY_EXISTS;
        }
    }
    
    // 添加新的处理函数
    mcu_command_handler_t *handler_entry = &g_command_handlers[g_handler_count];
    handler_entry->command_start = command_start;
    handler_entry->command_end = command_end;
    handler_entry->callback = handler;
    handler_entry->user_data = user_data;
    handler_entry->call_count = 0;
    handler_entry->error_count = 0;
    
    g_handler_count++;
    
    rt_mutex_release(g_processor_mutex);
    return MCU_UART_OK;
}

/**
 * @brief 注销命令处理函数
 * 
 * @param command_start 命令起始范围
 * @param command_end 命令结束范围
 * @return mcu_uart_result_e 操作结果
 */
mcu_uart_result_e mcu_command_processor_unregister_handler(uint16_t command_start,
                                                            uint16_t command_end)
{
    if (!g_processor_initialized) {
        return MCU_UART_INVALID_PARAM;
    }
    
    rt_mutex_take(g_processor_mutex, RT_WAITING_FOREVER);
    
    for (uint32_t i = 0; i < g_handler_count; i++) {
        if (g_command_handlers[i].command_start == command_start &&
            g_command_handlers[i].command_end == command_end) {
            // 找到匹配的处理函数，删除它
            for (uint32_t j = i; j < g_handler_count - 1; j++) {
                memcpy(&g_command_handlers[j], &g_command_handlers[j + 1], 
                       sizeof(mcu_command_handler_t));
            }
            g_handler_count--;
            rt_mutex_release(g_processor_mutex);
            return MCU_UART_OK;
        }
    }
    
    rt_mutex_release(g_processor_mutex);
    return MCU_UART_NOT_FOUND;
}

/* ========================================================================== */
/*                           统计信息接口                                     */
/* ========================================================================== */

/**
 * @brief 获取处理器统计信息
 * 
 * @param stats 统计信息结构体
 * @return mcu_uart_result_e 操作结果
 */
mcu_uart_result_e mcu_command_processor_get_stats(mcu_command_processor_stats_t *stats)
{
    if (!g_processor_initialized || !stats) {
        return MCU_UART_INVALID_PARAM;
    }
    
    rt_mutex_take(g_processor_mutex, RT_WAITING_FOREVER);
    memcpy(stats, &g_command_processor.stats, sizeof(mcu_command_processor_stats_t));
    rt_mutex_release(g_processor_mutex);
    
    return MCU_UART_OK;
}

/**
 * @brief 重置处理器统计信息
 * 
 * @return mcu_uart_result_e 操作结果
 */
mcu_uart_result_e mcu_command_processor_reset_stats(void)
{
    if (!g_processor_initialized) {
        return MCU_UART_INVALID_PARAM;
    }
    
    rt_mutex_take(g_processor_mutex, RT_WAITING_FOREVER);
    memset(&g_command_processor.stats, 0, sizeof(mcu_command_processor_stats_t));
    g_command_processor.stats.start_time = rt_tick_get();
    rt_mutex_release(g_processor_mutex);
    
    return MCU_UART_OK;
}

/* ========================================================================== */
/*                             内部函数                                       */
/* ========================================================================== */

/**
 * @brief 分发命令到相应的处理函数
 */
static mcu_uart_result_e mcu_command_dispatch(const mcu_protocol_packet_t *packet, 
                                               mcu_protocol_packet_t *response)
{
    uint16_t command = packet->command;
    
    // 首先检查是否有注册的处理函数
    for (uint32_t i = 0; i < g_handler_count; i++) {
        if (command >= g_command_handlers[i].command_start &&
            command <= g_command_handlers[i].command_end) {
            
            g_command_handlers[i].call_count++;
            
            mcu_uart_result_e result = g_command_handlers[i].callback(packet, 
                                                                      response, 
                                                                      g_command_handlers[i].user_data);
            
            if (result != MCU_UART_OK) {
                g_command_handlers[i].error_count++;
            }
            
            return result;
        }
    }
    
    // 如果没有注册处理函数，使用默认处理逻辑
    if (mcu_command_is_transparent(command)) {
        return mcu_command_process_transparent(packet, response);
    } else if (mcu_command_is_send(command)) {
        return mcu_command_process_send(packet, response);
    } else if (mcu_command_is_receive(command)) {
        return mcu_command_process_receive(packet, response);
    }
    
    return MCU_UART_UNSUPPORTED_COMMAND;
}

/**
 * @brief 处理透传命令
 */
static mcu_uart_result_e mcu_command_process_transparent(const mcu_protocol_packet_t *packet, 
                                                          mcu_protocol_packet_t *response)
{
    // 透传命令的默认处理：直接转发
    if (response) {
        memcpy(response, packet, sizeof(mcu_protocol_packet_t));
        response->packet_type = MCU_PACKET_TYPE_RECV;
    }
    
    g_command_processor.stats.transparent_commands++;
    return MCU_UART_OK;
}

/**
 * @brief 处理发送命令
 */
static mcu_uart_result_e mcu_command_process_send(const mcu_protocol_packet_t *packet, 
                                                   mcu_protocol_packet_t *response)
{
    // 发送命令的默认处理：生成ACK响应
    if (response) {
        response->packet_type = MCU_PACKET_TYPE_RECV;
        response->command = packet->command | 0x8000; // ACK标志
        response->data_length = 2;
        
        // 假设有静态缓冲区存储ACK数据
        static uint8_t ack_data[2] = {0x00, 0x00}; // 成功ACK
        response->data = ack_data;
    }
    
    g_command_processor.stats.send_commands++;
    return MCU_UART_OK;
}

/**
 * @brief 处理接收命令
 */
static mcu_uart_result_e mcu_command_process_receive(const mcu_protocol_packet_t *packet, 
                                                      mcu_protocol_packet_t *response)
{
    // 接收命令的默认处理：记录接收
    g_command_processor.stats.receive_commands++;
    return MCU_UART_OK;
}

/**
 * @brief 检查是否为透传命令
 */
static bool mcu_command_is_transparent(uint16_t command)
{
    return (command >= MCU_CMD_TRANSPARENT_START && command <= MCU_CMD_TRANSPARENT_END);
}

/**
 * @brief 检查是否为发送命令
 */
static bool mcu_command_is_send(uint16_t command)
{
    return (command >= MCU_CMD_SEND_START && command <= MCU_CMD_SEND_END);
}

/**
 * @brief 检查是否为接收命令
 */
static bool mcu_command_is_receive(uint16_t command)
{
    return (command >= MCU_CMD_RECEIVE_START && command <= MCU_CMD_RECEIVE_END);
}

/**
 * @brief 命令处理线程
 */
void mcu_command_processor_thread(void *parameter)
{
    mcu_command_item_t item;
    mcu_response_item_t response_item;
    
    while (1) {
        // 从命令队列获取命令
        if (rt_mq_recv(g_command_processor.command_queue, &item, sizeof(item), RT_WAITING_FOREVER) == RT_EOK) {
            
            // 检查命令是否超时
            uint32_t current_time = rt_tick_get();
            uint32_t elapsed_ms = rt_tick_to_millisecond(current_time - item.timestamp);
            
            if (item.timeout_ms != RT_WAITING_FOREVER && elapsed_ms > item.timeout_ms) {
                g_command_processor.stats.timeout_commands++;
                continue;
            }
            
            // 处理命令
            mcu_uart_result_e result = mcu_command_dispatch(&item.packet, &response_item.packet);
            
            // 如果有响应，放入响应队列
            if (result == MCU_UART_OK && response_item.packet.data_length > 0) {
                response_item.timestamp = rt_tick_get();
                rt_mq_send(g_command_processor.response_queue, &response_item, sizeof(response_item));
            }
        }
    }
} 