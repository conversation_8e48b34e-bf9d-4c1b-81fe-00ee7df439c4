/**
 * @file mcu_uart_protocol.c
 * @brief MCU UART增强版协议处理实现
 * @details 完整的协议编解码、校验和ACK机制实现
 * @version 1.0.0
 * @date 2025-01-19
 */

#include "mcu_uart_protocol.h"
#include "mcu_uart_commands.h"
#include <stdlib.h>

/* ========== 内部函数声明 ========== */

static __u16 mcu_uart_protocol_swap_bytes(__u16 value);
static bool mcu_uart_protocol_is_valid_tag(__u16 tag);
static mcu_uart_packet_format_t mcu_uart_protocol_get_format_by_tag(__u16 tag);

/* ========== 协议处理API实现 ========== */

/**
 * @brief 初始化协议处理器
 */
mcu_uart_error_t mcu_uart_protocol_init(mcu_uart_protocol_handler_t *handler,
                                        const mcu_uart_protocol_config_t *config)
{
    if (!handler || !config) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 清零结构 */
    memset(handler, 0, sizeof(mcu_uart_protocol_handler_t));
    
    /* 设置配置参数 */
    handler->ack_timeout_ms = config->ack_timeout_ms;
    handler->max_retry_count = config->max_resend_count;
    handler->auto_ack_enabled = config->enable_ack_wait;
    handler->sequence_check_enabled = true;
    handler->checksum_enabled = config->enable_checksum;
    handler->local_addr = config->device_address;
    
    /* 初始化状态 */
    handler->state = MCU_UART_PROTOCOL_STATE_IDLE;
    handler->next_sequence = 1;
    handler->expected_sequence = 1;
    handler->max_pending = 16;
    
    /* 分配待处理包队列 */
    handler->pending_packets = (mcu_uart_packet_t*)malloc(
        handler->max_pending * sizeof(mcu_uart_packet_t));
    if (!handler->pending_packets) {
        return MCU_UART_ERROR_NO_MEMORY;
    }
    
    /* 设置魔数 */
    handler->magic = MCU_UART_MAGIC_HANDLE;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 销毁协议处理器
 */
mcu_uart_error_t mcu_uart_protocol_deinit(mcu_uart_protocol_handler_t *handler)
{
    if (!mcu_uart_protocol_is_valid(handler)) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 释放待处理包队列 */
    if (handler->pending_packets) {
        free(handler->pending_packets);
        handler->pending_packets = NULL;
    }
    
    /* 清除魔数 */
    handler->magic = 0;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 重置协议处理器
 */
mcu_uart_error_t mcu_uart_protocol_reset(mcu_uart_protocol_handler_t *handler)
{
    if (!mcu_uart_protocol_is_valid(handler)) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 重置状态 */
    handler->state = MCU_UART_PROTOCOL_STATE_IDLE;
    handler->next_sequence = 1;
    handler->expected_sequence = 1;
    handler->pending_count = 0;
    
    /* 重置统计信息 */
    memset(&handler->stats, 0, sizeof(handler->stats));
    
    return MCU_UART_ERROR_NONE;
}

/* ========== 包编码API实现 ========== */

/**
 * @brief 构建标准数据包
 */
mcu_uart_error_t mcu_uart_protocol_build_packet(
    __u16 command,
    const __u8 *data,
    __u32 data_len,
    __u8 *packet_buffer,
    __u32 *buffer_size)
{
    __u32 required_size;
    __u32 offset = 0;
    __u8 checksum = 0;
    __u32 i;
    
    if (!packet_buffer || !buffer_size) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (data_len > 0 && !data) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 计算所需缓冲区大小 */
    required_size = MCU_UART_HEAD_LEN + data_len + MCU_UART_CHECKSUM_LEN + MCU_UART_END_LEN;
    
    if (*buffer_size < required_size) {
        *buffer_size = required_size;
        return MCU_UART_ERROR_BUFFER_OVERFLOW;
    }
    
    /* 构建包头 */
    packet_buffer[offset++] = MCU_UART_LOBYTE(MCU_UART_SEND_TAG);
    packet_buffer[offset++] = MCU_UART_HIBYTE(MCU_UART_SEND_TAG);
    packet_buffer[offset++] = MCU_UART_LOBYTE(data_len);
    packet_buffer[offset++] = MCU_UART_HIBYTE(data_len);
    packet_buffer[offset++] = MCU_UART_LOBYTE(command);
    packet_buffer[offset++] = MCU_UART_HIBYTE(command);
    
    /* 添加数据 */
    if (data_len > 0) {
        MCU_UART_MEMCPY(&packet_buffer[offset], data, data_len);
        offset += data_len;
    }
    
    /* 计算校验和(从长度字段开始) */
    for (i = 2; i < offset; i++) {
        checksum += packet_buffer[i];
    }
    packet_buffer[offset++] = checksum;
    
    /* 添加结束标签 */
    packet_buffer[offset++] = MCU_UART_LOBYTE(MCU_UART_END_TAG);
    packet_buffer[offset++] = MCU_UART_HIBYTE(MCU_UART_END_TAG);
    
    *buffer_size = offset;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 构建远程数据包
 */
mcu_uart_error_t mcu_uart_protocol_build_remote_packet(
    __u16 command,
    mcu_uart_device_addr_t device_addr,
    __u8 zone_mask,
    bool ack_required,
    const __u8 *data,
    __u32 data_len,
    __u8 *packet_buffer,
    __u32 *buffer_size)
{
    __u32 required_size;
    __u32 offset = 0;
    __u8 checksum = 0;
    __u32 i;
    
    if (!packet_buffer || !buffer_size) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (data_len > 0 && !data) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 计算所需缓冲区大小 */
    required_size = MCU_UART_REMOTE_HEAD_LEN + data_len + MCU_UART_CHECKSUM_LEN + MCU_UART_END_LEN;
    
    if (*buffer_size < required_size) {
        *buffer_size = required_size;
        return MCU_UART_ERROR_BUFFER_OVERFLOW;
    }
    
    /* 构建远程包头 */
    packet_buffer[offset++] = MCU_UART_LOBYTE(MCU_UART_SEND_TAG);
    packet_buffer[offset++] = MCU_UART_HIBYTE(MCU_UART_SEND_TAG);
    packet_buffer[offset++] = MCU_UART_LOBYTE(data_len);
    packet_buffer[offset++] = MCU_UART_HIBYTE(data_len);
    packet_buffer[offset++] = MCU_UART_LOBYTE(command);
    packet_buffer[offset++] = MCU_UART_HIBYTE(command);
    
    /* 添加设备地址 */
#ifdef REMOTE_ADDR_4BYTES
    packet_buffer[offset++] = (device_addr >> 0) & 0xFF;
    packet_buffer[offset++] = (device_addr >> 8) & 0xFF;
    packet_buffer[offset++] = (device_addr >> 16) & 0xFF;
    packet_buffer[offset++] = (device_addr >> 24) & 0xFF;
#else
    packet_buffer[offset++] = MCU_UART_LOBYTE(device_addr);
    packet_buffer[offset++] = MCU_UART_HIBYTE(device_addr);
#endif
    
    /* 添加区域掩码和ACK标志 */
    packet_buffer[offset++] = zone_mask;
    packet_buffer[offset++] = ack_required ? 1 : 0;
    
    /* 添加数据 */
    if (data_len > 0) {
        MCU_UART_MEMCPY(&packet_buffer[offset], data, data_len);
        offset += data_len;
    }
    
    /* 计算校验和(从长度字段开始) */
    for (i = 2; i < offset; i++) {
        checksum += packet_buffer[i];
    }
    packet_buffer[offset++] = checksum;
    
    /* 添加结束标签 */
    packet_buffer[offset++] = MCU_UART_LOBYTE(MCU_UART_END_TAG);
    packet_buffer[offset++] = MCU_UART_HIBYTE(MCU_UART_END_TAG);
    
    *buffer_size = offset;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 构建ACK包
 */
mcu_uart_error_t mcu_uart_protocol_build_ack_packet(
    __u8 sequence,
    __u8 ack_type,
    __u8 *packet_buffer,
    __u32 *buffer_size)
{
    __u32 required_size = MCU_UART_HEAD_LEN + 2 + MCU_UART_CHECKSUM_LEN + MCU_UART_END_LEN;
    __u32 offset = 0;
    __u8 checksum = 0;
    __u32 i;
    
    if (!packet_buffer || !buffer_size) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (*buffer_size < required_size) {
        *buffer_size = required_size;
        return MCU_UART_ERROR_BUFFER_OVERFLOW;
    }
    
    /* 构建ACK包 */
    packet_buffer[offset++] = MCU_UART_LOBYTE(MCU_UART_SEND_TAG);
    packet_buffer[offset++] = MCU_UART_HIBYTE(MCU_UART_SEND_TAG);
    packet_buffer[offset++] = 2; /* 数据长度 */
    packet_buffer[offset++] = 0;
    packet_buffer[offset++] = 0; /* ACK命令 */
    packet_buffer[offset++] = 0;
    packet_buffer[offset++] = sequence;
    packet_buffer[offset++] = ack_type;
    
    /* 计算校验和 */
    for (i = 2; i < offset; i++) {
        checksum += packet_buffer[i];
    }
    packet_buffer[offset++] = checksum;
    
    /* 添加结束标签 */
    packet_buffer[offset++] = MCU_UART_LOBYTE(MCU_UART_END_TAG);
    packet_buffer[offset++] = MCU_UART_HIBYTE(MCU_UART_END_TAG);
    
    *buffer_size = offset;
    
    return MCU_UART_ERROR_NONE;
}

/* ========== 包解码API实现 ========== */

/**
 * @brief 解析数据包
 */
mcu_uart_error_t mcu_uart_protocol_parse_packet(
    const __u8 *raw_data,
    __u32 data_len,
    mcu_uart_packet_t *packet)
{
    __u32 offset = 0;
    __u16 start_tag;
    __u16 length;
    __u16 command;
    __u8 calculated_checksum = 0;
    __u32 i;
    
    if (!raw_data || !packet || data_len < MCU_UART_HEAD_LEN + MCU_UART_CHECKSUM_LEN + MCU_UART_END_LEN) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 清零包结构 */
    memset(packet, 0, sizeof(mcu_uart_packet_t));
    
    /* 解析起始标签 */
    start_tag = MCU_UART_MAKEWORD(raw_data[offset + 1], raw_data[offset]);
    offset += 2;
    
    /* 检查起始标签 */
    if (!mcu_uart_protocol_is_valid_tag(start_tag)) {
        return MCU_UART_ERROR_PROTOCOL_ERROR;
    }
    
    /* 确定包格式 */
    packet->format = mcu_uart_protocol_get_format_by_tag(start_tag);
    
    /* 解析长度 */
    length = MCU_UART_MAKEWORD(raw_data[offset + 1], raw_data[offset]);
    offset += 2;
    
    /* 解析命令 */
    command = MCU_UART_MAKEWORD(raw_data[offset + 1], raw_data[offset]);
    offset += 2;
    
    /* 检查包长度 */
    if (data_len < MCU_UART_HEAD_LEN + length + MCU_UART_CHECKSUM_LEN + MCU_UART_END_LEN) {
        return MCU_UART_ERROR_PACKET_INVALID;
    }
    
    /* 填充包头信息 */
    if (packet->format == MCU_UART_FORMAT_SEND || packet->format == MCU_UART_FORMAT_RECV) {
        packet->header.std.start_tag = start_tag;
        packet->header.std.length = length;
        packet->header.std.command = command;
        packet->header.std.sequence = 0; /* 标准包没有序列号 */
        packet->header.std.flags = 0;
    } else {
        /* 远程包格式 */
        packet->header.remote.start_tag = start_tag;
        packet->header.remote.length = length;
        packet->header.remote.command = command;
        
        /* 解析设备地址 */
#ifdef REMOTE_ADDR_4BYTES
        packet->header.remote.device_addr = MCU_UART_MAKEINT(
            raw_data[offset + 3], raw_data[offset + 2], 
            raw_data[offset + 1], raw_data[offset]);
        offset += 4;
#else
        packet->header.remote.device_addr = MCU_UART_MAKEWORD(
            raw_data[offset + 1], raw_data[offset]);
        offset += 2;
#endif
        
        /* 解析区域掩码和ACK */
        packet->header.remote.zone_mask = raw_data[offset++];
        packet->header.remote.ack = raw_data[offset++];
    }
    
    /* 设置数据指针和长度 */
    packet->data_length = length;
    if (length > 0) {
        packet->data = (__u8*)&raw_data[offset];
        offset += length;
    }
    
    /* 验证校验和 */
    packet->checksum = raw_data[offset++];
    for (i = 2; i < offset - 1; i++) {
        calculated_checksum += raw_data[i];
    }
    
    if (calculated_checksum != packet->checksum) {
        return MCU_UART_ERROR_CHECKSUM_FAIL;
    }
    
    /* 验证结束标签 */
    if (offset + 1 < data_len) {
        __u16 end_tag = MCU_UART_MAKEWORD(raw_data[offset + 1], raw_data[offset]);
        if (end_tag != MCU_UART_END_TAG) {
            return MCU_UART_ERROR_PROTOCOL_ERROR;
        }
        packet->end_tag = end_tag;
    }
    
    /* 设置时间戳 */
    packet->timestamp = ktime_get();
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 验证数据包
 */
mcu_uart_error_t mcu_uart_protocol_validate_packet(const mcu_uart_packet_t *packet)
{
    if (!packet) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 检查包格式 */
    if (packet->format >= MCU_UART_FORMAT_MAX) {
        return MCU_UART_ERROR_PROTOCOL_ERROR;
    }
    
    /* 检查数据长度 */
    if (packet->data_length > MCU_UART_MAX_PACKET_LEN) {
        return MCU_UART_ERROR_PACKET_TOO_LARGE;
    }
    
    /* 检查数据指针 */
    if (packet->data_length > 0 && !packet->data) {
        return MCU_UART_ERROR_PACKET_INVALID;
    }
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 检测包格式
 */
mcu_uart_error_t mcu_uart_protocol_detect_format(
    const __u8 *raw_data,
    __u32 data_len,
    mcu_uart_packet_format_t *format)
{
    __u16 start_tag;
    
    if (!raw_data || !format || data_len < 2) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    start_tag = MCU_UART_MAKEWORD(raw_data[1], raw_data[0]);
    
    if (start_tag == MCU_UART_SEND_TAG) {
        *format = MCU_UART_FORMAT_SEND;
    } else if (start_tag == MCU_UART_RECV_TAG) {
        *format = MCU_UART_FORMAT_RECV;
    } else if (raw_data[0] == MCU_UART_UART0_TAG) {
        *format = MCU_UART_FORMAT_UART0;
    } else {
        return MCU_UART_ERROR_PROTOCOL_ERROR;
    }
    
    return MCU_UART_ERROR_NONE;
}

/* ========== 校验和API实现 ========== */

/**
 * @brief 计算校验和
 */
__u8 mcu_uart_protocol_calculate_checksum(const __u8 *data, __u32 len)
{
    __u8 checksum = 0;
    __u32 i;
    
    if (!data || len == 0) {
        return 0;
    }
    
    for (i = 0; i < len; i++) {
        checksum += data[i];
    }
    
    return checksum;
}

/**
 * @brief 验证校验和
 */
bool mcu_uart_protocol_verify_checksum(const __u8 *data, __u32 len, __u8 expected_checksum)
{
    __u8 calculated = mcu_uart_protocol_calculate_checksum(data, len);
    return (calculated == expected_checksum);
}

/**
 * @brief 计算CRC16
 */
__u16 mcu_uart_protocol_calculate_crc16(const __u8 *data, __u32 len)
{
    __u16 crc = 0xFFFF;
    __u32 i, j;
    
    if (!data || len == 0) {
        return 0;
    }
    
    for (i = 0; i < len; i++) {
        crc ^= data[i];
        for (j = 0; j < 8; j++) {
            if (crc & 1) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc >>= 1;
            }
        }
    }
    
    return crc;
}

/**
 * @brief 验证CRC16
 */
bool mcu_uart_protocol_verify_crc16(const __u8 *data, __u32 len, __u16 expected_crc)
{
    __u16 calculated = mcu_uart_protocol_calculate_crc16(data, len);
    return (calculated == expected_crc);
}

/* ========== 内部函数实现 ========== */

/**
 * @brief 字节交换
 */
static __u16 mcu_uart_protocol_swap_bytes(__u16 value)
{
    return ((value & 0xFF) << 8) | ((value >> 8) & 0xFF);
}

/**
 * @brief 检查标签是否有效
 */
static bool mcu_uart_protocol_is_valid_tag(__u16 tag)
{
    return (tag == MCU_UART_SEND_TAG || 
            tag == MCU_UART_RECV_TAG || 
            (tag & 0xFF) == MCU_UART_UART0_TAG);
}

/**
 * @brief 根据标签获取格式
 */
static mcu_uart_packet_format_t mcu_uart_protocol_get_format_by_tag(__u16 tag)
{
    if (tag == MCU_UART_SEND_TAG) {
        return MCU_UART_FORMAT_SEND;
    } else if (tag == MCU_UART_RECV_TAG) {
        return MCU_UART_FORMAT_RECV;
    } else {
        return MCU_UART_FORMAT_UART0;
    }
}
