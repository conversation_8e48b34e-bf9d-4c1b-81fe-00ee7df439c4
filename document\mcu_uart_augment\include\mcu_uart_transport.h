/**
 * @file mcu_uart_transport.h
 * @brief MCU UART增强版传输层模块
 * @details 完整的UART硬件抽象、中断处理和流控制
 * @version 1.0.0
 * @date 2025-01-19
 */

#ifndef MCU_UART_TRANSPORT_H
#define MCU_UART_TRANSPORT_H

#ifdef __cplusplus
extern "C" {
#endif

#include "mcu_uart_types.h"
#include "mcu_uart_config.h"
#include "mcu_uart_buffer.h"

/* ========== 传输层常量定义 ========== */

/** @brief 传输状态定义 */
typedef enum mcu_uart_transport_state_e {
    MCU_UART_TRANSPORT_STATE_UNINITIALIZED = 0, /**< 未初始化 */
    MCU_UART_TRANSPORT_STATE_INITIALIZING,      /**< 初始化中 */
    MCU_UART_TRANSPORT_STATE_READY,             /**< 就绪 */
    MCU_UART_TRANSPORT_STATE_TRANSMITTING,      /**< 发送中 */
    MCU_UART_TRANSPORT_STATE_RECEIVING,         /**< 接收中 */
    MCU_UART_TRANSPORT_STATE_ERROR,             /**< 错误状态 */
    MCU_UART_TRANSPORT_STATE_SUSPENDED,         /**< 挂起状态 */
    MCU_UART_TRANSPORT_STATE_MAX
} mcu_uart_transport_state_t;

/** @brief 传输模式定义 */
typedef enum mcu_uart_transport_mode_e {
    MCU_UART_TRANSPORT_MODE_POLLING = 0,        /**< 轮询模式 */
    MCU_UART_TRANSPORT_MODE_INTERRUPT,          /**< 中断模式 */
    MCU_UART_TRANSPORT_MODE_DMA,                /**< DMA模式 */
    MCU_UART_TRANSPORT_MODE_MAX
} mcu_uart_transport_mode_t;

/** @brief 流控制类型 */
typedef enum mcu_uart_flow_control_e {
    MCU_UART_FLOW_CONTROL_NONE = 0,             /**< 无流控 */
    MCU_UART_FLOW_CONTROL_SOFTWARE,             /**< 软件流控(XON/XOFF) */
    MCU_UART_FLOW_CONTROL_HARDWARE,             /**< 硬件流控(RTS/CTS) */
    MCU_UART_FLOW_CONTROL_MAX
} mcu_uart_flow_control_t;

/** @brief 中断类型定义 */
typedef enum mcu_uart_interrupt_type_e {
    MCU_UART_INT_TYPE_RX_READY = 0,             /**< 接收就绪中断 */
    MCU_UART_INT_TYPE_TX_EMPTY,                 /**< 发送空中断 */
    MCU_UART_INT_TYPE_ERROR,                    /**< 错误中断 */
    MCU_UART_INT_TYPE_BREAK,                    /**< 中断信号 */
    MCU_UART_INT_TYPE_TIMEOUT,                  /**< 超时中断 */
    MCU_UART_INT_TYPE_MAX
} mcu_uart_interrupt_type_t;

/* ========== 传输层结构定义 ========== */

/**
 * @brief UART硬件配置结构
 */
typedef struct mcu_uart_hw_params_s {
    __u32 baudrate;                            /**< 波特率 */
    __u8 data_bits;                            /**< 数据位(5-8) */
    __u8 stop_bits;                            /**< 停止位(1-2) */
    __u8 parity;                               /**< 校验位(0=无,1=奇,2=偶) */
    mcu_uart_flow_control_t flow_control;      /**< 流控制类型 */
    __u32 tx_fifo_size;                        /**< 发送FIFO大小 */
    __u32 rx_fifo_size;                        /**< 接收FIFO大小 */
    __u32 timeout_ms;                          /**< 超时时间 */
} mcu_uart_hw_params_t;

/**
 * @brief 传输统计信息
 */
typedef struct mcu_uart_transport_stats_s {
    __u32 bytes_sent;                          /**< 发送字节数 */
    __u32 bytes_received;                      /**< 接收字节数 */
    __u32 packets_sent;                        /**< 发送包数 */
    __u32 packets_received;                    /**< 接收包数 */
    __u32 tx_interrupts;                       /**< 发送中断次数 */
    __u32 rx_interrupts;                       /**< 接收中断次数 */
    __u32 error_interrupts;                    /**< 错误中断次数 */
    __u32 tx_overruns;                         /**< 发送溢出次数 */
    __u32 rx_overruns;                         /**< 接收溢出次数 */
    __u32 framing_errors;                      /**< 帧错误次数 */
    __u32 parity_errors;                       /**< 校验错误次数 */
    __u32 break_errors;                        /**< 中断错误次数 */
    __u32 timeout_errors;                      /**< 超时错误次数 */
    __u32 flow_control_events;                 /**< 流控事件次数 */
    __u32 state_changes;                       /**< 状态变化次数 */
    __u32 avg_tx_speed_bps;                    /**< 平均发送速度 */
    __u32 avg_rx_speed_bps;                    /**< 平均接收速度 */
    __u32 max_tx_latency_us;                   /**< 最大发送延迟 */
    __u32 max_rx_latency_us;                   /**< 最大接收延迟 */
} mcu_uart_transport_stats_t;

/**
 * @brief 中断处理器结构
 */
typedef struct mcu_uart_interrupt_handler_s {
    void (*rx_handler)(void *context);         /**< 接收中断处理器 */
    void (*tx_handler)(void *context);         /**< 发送中断处理器 */
    void (*error_handler)(void *context, __u32 error_flags); /**< 错误中断处理器 */
    void (*timeout_handler)(void *context);    /**< 超时中断处理器 */
    void *context;                             /**< 中断上下文 */
} mcu_uart_interrupt_handler_t;

/**
 * @brief 传输层句柄结构
 */
typedef struct mcu_uart_transport_handle_s {
    /* 基本信息 */
    mcu_uart_channel_t channel;                /**< 通道类型 */
    mcu_uart_transport_state_t state;          /**< 传输状态 */
    mcu_uart_transport_mode_t mode;            /**< 传输模式 */
    
    /* 硬件相关 */
    ES_FILE *uart_handle;                      /**< UART句柄 */
    const char *device_path;                   /**< 设备路径 */
    mcu_uart_hw_params_t hw_params;            /**< 硬件参数 */
    
    /* 缓冲区 */
    mcu_uart_ring_buffer_t *tx_buffer;         /**< 发送缓冲区 */
    mcu_uart_ring_buffer_t *rx_buffer;         /**< 接收缓冲区 */
    __u8 *temp_buffer;                         /**< 临时缓冲区 */
    __u32 temp_buffer_size;                    /**< 临时缓冲区大小 */
    
    /* 中断处理 */
    mcu_uart_interrupt_handler_t interrupt;    /**< 中断处理器 */
    bool interrupt_enabled;                    /**< 中断是否启用 */
    __u32 interrupt_mask;                      /**< 中断掩码 */
    
    /* 流控制 */
    bool flow_control_active;                  /**< 流控是否激活 */
    bool tx_flow_stopped;                      /**< 发送流控停止 */
    bool rx_flow_stopped;                      /**< 接收流控停止 */
    __u32 xon_char;                            /**< XON字符 */
    __u32 xoff_char;                           /**< XOFF字符 */
    
    /* 统计信息 */
    mcu_uart_transport_stats_t stats;          /**< 统计信息 */
    __u32 last_activity_time;                  /**< 最后活动时间 */
    __u32 error_count;                         /**< 错误计数 */
    
    /* 回调函数 */
    void (*data_received_callback)(void *context, const __u8 *data, __u32 length);
    void (*data_sent_callback)(void *context, __u32 bytes_sent);
    void (*error_callback)(void *context, mcu_uart_error_t error);
    void (*state_change_callback)(void *context, mcu_uart_transport_state_t old_state, mcu_uart_transport_state_t new_state);
    void *callback_context;                    /**< 回调上下文 */
    
    /* 同步和保护 */
    void *lock;                                /**< 锁对象 */
    __u32 magic;                               /**< 魔数验证 */
} mcu_uart_transport_handle_t;

/* ========== 传输层API ========== */

/**
 * @brief 初始化传输层
 * @param[out] handle 传输层句柄指针
 * @param[in] channel 通道类型
 * @param[in] config 硬件配置
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_init(
    mcu_uart_transport_handle_t *handle,
    mcu_uart_channel_t channel,
    const mcu_uart_hw_config_t *config
);

/**
 * @brief 销毁传输层
 * @param[in] handle 传输层句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_deinit(mcu_uart_transport_handle_t *handle);

/**
 * @brief 打开传输通道
 * @param[in] handle 传输层句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_open(mcu_uart_transport_handle_t *handle);

/**
 * @brief 关闭传输通道
 * @param[in] handle 传输层句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_close(mcu_uart_transport_handle_t *handle);

/**
 * @brief 配置UART参数
 * @param[in] handle 传输层句柄指针
 * @param[in] params 硬件参数
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_configure(
    mcu_uart_transport_handle_t *handle,
    const mcu_uart_hw_params_t *params
);

/* ========== 数据传输API ========== */

/**
 * @brief 发送数据
 * @param[in] handle 传输层句柄指针
 * @param[in] data 发送数据
 * @param[in] length 数据长度
 * @param[out] bytes_sent 实际发送字节数
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_send(
    mcu_uart_transport_handle_t *handle,
    const __u8 *data,
    __u32 length,
    __u32 *bytes_sent
);

/**
 * @brief 异步发送数据
 * @param[in] handle 传输层句柄指针
 * @param[in] data 发送数据
 * @param[in] length 数据长度
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_send_async(
    mcu_uart_transport_handle_t *handle,
    const __u8 *data,
    __u32 length
);

/**
 * @brief 接收数据
 * @param[in] handle 传输层句柄指针
 * @param[out] data 接收数据缓冲区
 * @param[in] max_length 最大接收长度
 * @param[out] bytes_received 实际接收字节数
 * @param[in] timeout_ms 超时时间
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_receive(
    mcu_uart_transport_handle_t *handle,
    __u8 *data,
    __u32 max_length,
    __u32 *bytes_received,
    __u32 timeout_ms
);

/**
 * @brief 刷新发送缓冲区
 * @param[in] handle 传输层句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_flush_tx(mcu_uart_transport_handle_t *handle);

/**
 * @brief 刷新接收缓冲区
 * @param[in] handle 传输层句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_flush_rx(mcu_uart_transport_handle_t *handle);

/* ========== 中断处理API ========== */

/**
 * @brief 注册中断处理器
 * @param[in] handle 传输层句柄指针
 * @param[in] interrupt_handler 中断处理器结构
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_register_interrupt(
    mcu_uart_transport_handle_t *handle,
    const mcu_uart_interrupt_handler_t *interrupt_handler
);

/**
 * @brief 启用中断
 * @param[in] handle 传输层句柄指针
 * @param[in] interrupt_mask 中断掩码
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_enable_interrupt(
    mcu_uart_transport_handle_t *handle,
    __u32 interrupt_mask
);

/**
 * @brief 禁用中断
 * @param[in] handle 传输层句柄指针
 * @param[in] interrupt_mask 中断掩码
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_disable_interrupt(
    mcu_uart_transport_handle_t *handle,
    __u32 interrupt_mask
);

/**
 * @brief 中断服务程序
 * @param[in] handle 传输层句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_isr(mcu_uart_transport_handle_t *handle);

/* ========== 流控制API ========== */

/**
 * @brief 启用流控制
 * @param[in] handle 传输层句柄指针
 * @param[in] flow_control 流控制类型
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_enable_flow_control(
    mcu_uart_transport_handle_t *handle,
    mcu_uart_flow_control_t flow_control
);

/**
 * @brief 禁用流控制
 * @param[in] handle 传输层句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_disable_flow_control(mcu_uart_transport_handle_t *handle);

/**
 * @brief 发送XON信号
 * @param[in] handle 传输层句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_send_xon(mcu_uart_transport_handle_t *handle);

/**
 * @brief 发送XOFF信号
 * @param[in] handle 传输层句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_send_xoff(mcu_uart_transport_handle_t *handle);

/* ========== 状态查询API ========== */

/**
 * @brief 获取传输状态
 * @param[in] handle 传输层句柄指针
 * @return 传输状态
 */
mcu_uart_transport_state_t mcu_uart_transport_get_state(const mcu_uart_transport_handle_t *handle);

/**
 * @brief 检查是否可以发送
 * @param[in] handle 传输层句柄指针
 * @return true=可以发送, false=不可以发送
 */
bool mcu_uart_transport_can_send(const mcu_uart_transport_handle_t *handle);

/**
 * @brief 检查是否有数据可接收
 * @param[in] handle 传输层句柄指针
 * @return true=有数据, false=无数据
 */
bool mcu_uart_transport_has_data(const mcu_uart_transport_handle_t *handle);

/**
 * @brief 获取发送缓冲区可用空间
 * @param[in] handle 传输层句柄指针
 * @return 可用空间大小
 */
__u32 mcu_uart_transport_get_tx_space(const mcu_uart_transport_handle_t *handle);

/**
 * @brief 获取接收缓冲区数据量
 * @param[in] handle 传输层句柄指针
 * @return 数据量
 */
__u32 mcu_uart_transport_get_rx_count(const mcu_uart_transport_handle_t *handle);

/* ========== 回调函数API ========== */

/**
 * @brief 注册数据接收回调
 * @param[in] handle 传输层句柄指针
 * @param[in] callback 回调函数
 * @param[in] context 回调上下文
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_register_rx_callback(
    mcu_uart_transport_handle_t *handle,
    void (*callback)(void *context, const __u8 *data, __u32 length),
    void *context
);

/**
 * @brief 注册数据发送回调
 * @param[in] handle 传输层句柄指针
 * @param[in] callback 回调函数
 * @param[in] context 回调上下文
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_register_tx_callback(
    mcu_uart_transport_handle_t *handle,
    void (*callback)(void *context, __u32 bytes_sent),
    void *context
);

/**
 * @brief 注册错误回调
 * @param[in] handle 传输层句柄指针
 * @param[in] callback 回调函数
 * @param[in] context 回调上下文
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_register_error_callback(
    mcu_uart_transport_handle_t *handle,
    void (*callback)(void *context, mcu_uart_error_t error),
    void *context
);

/* ========== 统计和调试API ========== */

/**
 * @brief 获取传输统计信息
 * @param[in] handle 传输层句柄指针
 * @param[out] stats 统计信息结构
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_get_stats(
    const mcu_uart_transport_handle_t *handle,
    mcu_uart_transport_stats_t *stats
);

/**
 * @brief 重置传输统计信息
 * @param[in] handle 传输层句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_reset_stats(mcu_uart_transport_handle_t *handle);

/**
 * @brief 打印传输状态
 * @param[in] handle 传输层句柄指针
 */
void mcu_uart_transport_print_status(const mcu_uart_transport_handle_t *handle);

/**
 * @brief 执行自检
 * @param[in] handle 传输层句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_transport_self_test(mcu_uart_transport_handle_t *handle);

/* ========== 工具函数 ========== */

/**
 * @brief 获取传输状态字符串
 * @param[in] state 传输状态
 * @return 状态字符串
 */
const char* mcu_uart_transport_get_state_string(mcu_uart_transport_state_t state);

/**
 * @brief 获取传输模式字符串
 * @param[in] mode 传输模式
 * @return 模式字符串
 */
const char* mcu_uart_transport_get_mode_string(mcu_uart_transport_mode_t mode);

/**
 * @brief 检查传输句柄是否有效
 * @param[in] handle 传输层句柄指针
 * @return true=有效, false=无效
 */
static inline bool mcu_uart_transport_is_valid(const mcu_uart_transport_handle_t *handle)
{
    return MCU_UART_CHECK_MAGIC(handle, MCU_UART_MAGIC_HANDLE);
}

/* ========== 向前兼容性定义 ========== */

/** @brief 保持与原有结构的兼容性 */
typedef mcu_uart_transport_handle_t transport_handle_t;
typedef mcu_uart_transport_stats_t transport_stats_t;

/** @brief 保持与原有函数的兼容性 */
#define transport_init                  mcu_uart_transport_init
#define transport_send                  mcu_uart_transport_send
#define transport_receive               mcu_uart_transport_receive
#define transport_get_stats             mcu_uart_transport_get_stats

#ifdef __cplusplus
}
#endif

#endif /* MCU_UART_TRANSPORT_H */
