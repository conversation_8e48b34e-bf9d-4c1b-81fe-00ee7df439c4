/**
 * @file mcu_uart_buffer.c
 * @brief MCU UART增强版缓冲区管理实现
 * @version 1.0.0
 * @date 2025-01-19
 */

#include "mcu_uart_buffer.h"
#include <stdlib.h>

/* ========== 环形缓冲区API实现 ========== */

/**
 * @brief 初始化环形缓冲区
 */
mcu_uart_error_t mcu_uart_ring_buffer_init(
    mcu_uart_ring_buffer_t *buffer,
    __u8 *data,
    __u32 size,
    mcu_uart_buffer_mode_t mode)
{
    if (!buffer || !data || size == 0) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 清零结构 */
    MCU_UART_MEMSET(buffer, 0, sizeof(mcu_uart_ring_buffer_t));
    
    /* 设置基本参数 */
    buffer->data = data;
    buffer->size = size;
    buffer->head = 0;
    buffer->tail = 0;
    buffer->count = 0;
    buffer->mode = mode;
    buffer->state = MCU_UART_BUFFER_STATE_EMPTY;
    
    /* 设置水位线 */
    buffer->watermark_high = size * 80 / 100;  /* 80% */
    buffer->watermark_low = size * 20 / 100;   /* 20% */
    
    /* 设置魔数 */
    buffer->magic = MCU_UART_MAGIC_SEQUEL;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 销毁环形缓冲区
 */
mcu_uart_error_t mcu_uart_ring_buffer_deinit(mcu_uart_ring_buffer_t *buffer)
{
    if (!buffer || buffer->magic != MCU_UART_MAGIC_SEQUEL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 清除魔数 */
    buffer->magic = 0;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 重置环形缓冲区
 */
mcu_uart_error_t mcu_uart_ring_buffer_reset(mcu_uart_ring_buffer_t *buffer)
{
    if (!buffer || buffer->magic != MCU_UART_MAGIC_SEQUEL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    buffer->head = 0;
    buffer->tail = 0;
    buffer->count = 0;
    buffer->state = MCU_UART_BUFFER_STATE_EMPTY;
    buffer->last_access_time = ktime_get();
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 向环形缓冲区写入数据
 */
__u32 mcu_uart_ring_buffer_write(
    mcu_uart_ring_buffer_t *buffer,
    const __u8 *data,
    __u32 len)
{
    __u32 written = 0;
    __u32 available_space;
    __u32 write_size;
    __u32 first_part, second_part;
    
    if (!buffer || !data || len == 0 || buffer->magic != MCU_UART_MAGIC_SEQUEL) {
        return 0;
    }
    
    /* 计算可用空间 */
    available_space = buffer->size - buffer->count;
    
    /* 根据模式处理 */
    if (buffer->mode == MCU_UART_BUFFER_MODE_OVERWRITE) {
        write_size = len;  /* 覆写模式，写入所有数据 */
    } else {
        write_size = (len > available_space) ? available_space : len;
    }
    
    if (write_size == 0) {
        buffer->overflow_count++;
        return 0;
    }
    
    /* 处理覆写模式下的数据丢弃 */
    if (buffer->mode == MCU_UART_BUFFER_MODE_OVERWRITE && len > available_space) {
        __u32 discard_count = len - available_space;
        buffer->tail = (buffer->tail + discard_count) % buffer->size;
        buffer->count -= discard_count;
        buffer->underflow_count++;
    }
    
    /* 计算写入的两个部分 */
    first_part = buffer->size - buffer->head;
    if (write_size <= first_part) {
        /* 一次写入完成 */
        MCU_UART_MEMCPY(&buffer->data[buffer->head], data, write_size);
        written = write_size;
    } else {
        /* 需要分两次写入 */
        MCU_UART_MEMCPY(&buffer->data[buffer->head], data, first_part);
        second_part = write_size - first_part;
        MCU_UART_MEMCPY(&buffer->data[0], &data[first_part], second_part);
        written = write_size;
    }
    
    /* 更新指针和计数 */
    buffer->head = (buffer->head + written) % buffer->size;
    buffer->count += written;
    buffer->total_written += written;
    
    /* 更新最大计数 */
    if (buffer->count > buffer->max_count) {
        buffer->max_count = buffer->count;
    }
    
    /* 更新状态 */
    if (buffer->count >= buffer->size) {
        buffer->state = MCU_UART_BUFFER_STATE_FULL;
    } else if (buffer->count >= buffer->watermark_high) {
        buffer->state = MCU_UART_BUFFER_STATE_FILLING;
    } else {
        buffer->state = MCU_UART_BUFFER_STATE_FILLING;
    }
    
    buffer->last_access_time = ktime_get();
    
    return written;
}

/**
 * @brief 从环形缓冲区读取数据
 */
__u32 mcu_uart_ring_buffer_read(
    mcu_uart_ring_buffer_t *buffer,
    __u8 *data,
    __u32 len)
{
    __u32 read_size;
    __u32 first_part, second_part;
    __u32 read_count = 0;
    
    if (!buffer || !data || len == 0 || buffer->magic != MCU_UART_MAGIC_SEQUEL) {
        return 0;
    }
    
    /* 计算可读取的数据量 */
    read_size = (len > buffer->count) ? buffer->count : len;
    
    if (read_size == 0) {
        return 0;
    }
    
    /* 计算读取的两个部分 */
    first_part = buffer->size - buffer->tail;
    if (read_size <= first_part) {
        /* 一次读取完成 */
        MCU_UART_MEMCPY(data, &buffer->data[buffer->tail], read_size);
        read_count = read_size;
    } else {
        /* 需要分两次读取 */
        MCU_UART_MEMCPY(data, &buffer->data[buffer->tail], first_part);
        second_part = read_size - first_part;
        MCU_UART_MEMCPY(&data[first_part], &buffer->data[0], second_part);
        read_count = read_size;
    }
    
    /* 更新指针和计数 */
    buffer->tail = (buffer->tail + read_count) % buffer->size;
    buffer->count -= read_count;
    buffer->total_read += read_count;
    
    /* 更新状态 */
    if (buffer->count == 0) {
        buffer->state = MCU_UART_BUFFER_STATE_EMPTY;
    } else if (buffer->count <= buffer->watermark_low) {
        buffer->state = MCU_UART_BUFFER_STATE_READING;
    } else {
        buffer->state = MCU_UART_BUFFER_STATE_READING;
    }
    
    buffer->last_access_time = ktime_get();
    
    return read_count;
}

/**
 * @brief 从环形缓冲区窥视数据(不移动读指针)
 */
__u32 mcu_uart_ring_buffer_peek(
    mcu_uart_ring_buffer_t *buffer,
    __u8 *data,
    __u32 len,
    __u32 offset)
{
    __u32 peek_size;
    __u32 peek_tail;
    __u32 first_part, second_part;
    __u32 peek_count = 0;
    
    if (!buffer || !data || len == 0 || buffer->magic != MCU_UART_MAGIC_SEQUEL) {
        return 0;
    }
    
    /* 检查偏移量 */
    if (offset >= buffer->count) {
        return 0;
    }
    
    /* 计算可窥视的数据量 */
    peek_size = buffer->count - offset;
    if (len < peek_size) {
        peek_size = len;
    }
    
    /* 计算窥视起始位置 */
    peek_tail = (buffer->tail + offset) % buffer->size;
    
    /* 计算窥视的两个部分 */
    first_part = buffer->size - peek_tail;
    if (peek_size <= first_part) {
        /* 一次窥视完成 */
        MCU_UART_MEMCPY(data, &buffer->data[peek_tail], peek_size);
        peek_count = peek_size;
    } else {
        /* 需要分两次窥视 */
        MCU_UART_MEMCPY(data, &buffer->data[peek_tail], first_part);
        second_part = peek_size - first_part;
        MCU_UART_MEMCPY(&data[first_part], &buffer->data[0], second_part);
        peek_count = peek_size;
    }
    
    return peek_count;
}

/**
 * @brief 跳过环形缓冲区中的数据
 */
__u32 mcu_uart_ring_buffer_skip(mcu_uart_ring_buffer_t *buffer, __u32 len)
{
    __u32 skip_size;
    
    if (!buffer || len == 0 || buffer->magic != MCU_UART_MAGIC_SEQUEL) {
        return 0;
    }
    
    /* 计算可跳过的数据量 */
    skip_size = (len > buffer->count) ? buffer->count : len;
    
    if (skip_size == 0) {
        return 0;
    }
    
    /* 更新指针和计数 */
    buffer->tail = (buffer->tail + skip_size) % buffer->size;
    buffer->count -= skip_size;
    buffer->total_read += skip_size;
    
    /* 更新状态 */
    if (buffer->count == 0) {
        buffer->state = MCU_UART_BUFFER_STATE_EMPTY;
    }
    
    buffer->last_access_time = ktime_get();
    
    return skip_size;
}

/**
 * @brief 在环形缓冲区中搜索模式
 */
mcu_uart_error_t mcu_uart_ring_buffer_search(
    mcu_uart_ring_buffer_t *buffer,
    const __u8 *pattern,
    __u32 pattern_len,
    __u32 *position)
{
    __u32 i, j;
    __u32 search_pos;
    bool found = false;
    
    if (!buffer || !pattern || pattern_len == 0 || !position || 
        buffer->magic != MCU_UART_MAGIC_SEQUEL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    if (pattern_len > buffer->count) {
        return MCU_UART_ERROR_NOT_FOUND;
    }
    
    /* 搜索模式 */
    for (i = 0; i <= buffer->count - pattern_len; i++) {
        search_pos = (buffer->tail + i) % buffer->size;
        found = true;
        
        for (j = 0; j < pattern_len; j++) {
            __u32 data_pos = (search_pos + j) % buffer->size;
            if (buffer->data[data_pos] != pattern[j]) {
                found = false;
                break;
            }
        }
        
        if (found) {
            *position = i;
            return MCU_UART_ERROR_NONE;
        }
    }
    
    return MCU_UART_ERROR_NOT_FOUND;
}

/**
 * @brief 获取环形缓冲区使用率(百分比)
 */
__u32 mcu_uart_ring_buffer_usage_percent(const mcu_uart_ring_buffer_t *buffer)
{
    if (!buffer || buffer->magic != MCU_UART_MAGIC_SEQUEL || buffer->size == 0) {
        return 0;
    }
    
    return (buffer->count * 100) / buffer->size;
}

/* ========== 内存池API实现 ========== */

/**
 * @brief 初始化内存池
 */
mcu_uart_error_t mcu_uart_memory_pool_init(
    mcu_uart_memory_pool_t *pool,
    __u8 *pool_data,
    __u32 pool_size,
    __u32 block_size)
{
    __u32 i;
    mcu_uart_memory_block_t *block;
    __u8 *data_ptr;
    
    if (!pool || !pool_data || pool_size == 0 || block_size == 0) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 清零结构 */
    MCU_UART_MEMSET(pool, 0, sizeof(mcu_uart_memory_pool_t));
    
    /* 设置基本参数 */
    pool->pool_data = pool_data;
    pool->pool_size = pool_size;
    pool->block_size = block_size;
    pool->block_count = pool_size / (block_size + sizeof(mcu_uart_memory_block_t));
    
    /* 初始化空闲链表 */
    pool->free_list = NULL;
    pool->used_list = NULL;
    
    data_ptr = pool_data;
    for (i = 0; i < pool->block_count; i++) {
        block = (mcu_uart_memory_block_t*)data_ptr;
        block->data = data_ptr + sizeof(mcu_uart_memory_block_t);
        block->size = block_size;
        block->is_free = true;
        block->magic = MCU_UART_MAGIC_SEQUEL;
        block->next = pool->free_list;
        pool->free_list = block;
        
        data_ptr += block_size + sizeof(mcu_uart_memory_block_t);
    }
    
    pool->free_blocks = pool->block_count;
    pool->allocated_blocks = 0;
    pool->magic = MCU_UART_MAGIC_SEQUEL;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 销毁内存池
 */
mcu_uart_error_t mcu_uart_memory_pool_deinit(mcu_uart_memory_pool_t *pool)
{
    if (!pool || pool->magic != MCU_UART_MAGIC_SEQUEL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 清除魔数 */
    pool->magic = 0;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 从内存池分配内存
 */
void* mcu_uart_memory_pool_alloc(mcu_uart_memory_pool_t *pool, __u32 size)
{
    mcu_uart_memory_block_t *block;
    
    if (!pool || size == 0 || pool->magic != MCU_UART_MAGIC_SEQUEL) {
        return NULL;
    }
    
    if (size > pool->block_size) {
        pool->allocation_failures++;
        return NULL;
    }
    
    if (pool->free_list == NULL) {
        pool->allocation_failures++;
        return NULL;
    }
    
    /* 从空闲链表中取出一个块 */
    block = pool->free_list;
    pool->free_list = block->next;
    
    /* 添加到已用链表 */
    block->next = pool->used_list;
    pool->used_list = block;
    block->is_free = false;
    
    /* 更新统计 */
    pool->free_blocks--;
    pool->allocated_blocks++;
    pool->total_allocations++;
    
    if (pool->allocated_blocks > pool->max_allocated) {
        pool->max_allocated = pool->allocated_blocks;
    }
    
    return block->data;
}

/**
 * @brief 释放内存到内存池
 */
mcu_uart_error_t mcu_uart_memory_pool_free(mcu_uart_memory_pool_t *pool, void *ptr)
{
    mcu_uart_memory_block_t *block, *prev;
    
    if (!pool || !ptr || pool->magic != MCU_UART_MAGIC_SEQUEL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 在已用链表中查找块 */
    prev = NULL;
    block = pool->used_list;
    while (block) {
        if (block->data == ptr) {
            break;
        }
        prev = block;
        block = block->next;
    }
    
    if (!block || block->is_free) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 从已用链表中移除 */
    if (prev) {
        prev->next = block->next;
    } else {
        pool->used_list = block->next;
    }
    
    /* 添加到空闲链表 */
    block->next = pool->free_list;
    pool->free_list = block;
    block->is_free = true;
    
    /* 更新统计 */
    pool->free_blocks++;
    pool->allocated_blocks--;
    pool->total_deallocations++;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 内存池垃圾回收
 */
__u32 mcu_uart_memory_pool_gc(mcu_uart_memory_pool_t *pool)
{
    /* 简单实现：内存池不需要垃圾回收 */
    if (!pool || pool->magic != MCU_UART_MAGIC_SEQUEL) {
        return 0;
    }
    
    return 0;
}

/* ========== 缓冲区管理器API实现 ========== */

/**
 * @brief 初始化缓冲区管理器
 */
mcu_uart_error_t mcu_uart_buffer_manager_init(
    mcu_uart_buffer_manager_t *manager,
    const mcu_uart_buffer_config_t *config)
{
    if (!manager || !config) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 清零结构 */
    MCU_UART_MEMSET(manager, 0, sizeof(mcu_uart_buffer_manager_t));
    
    /* 设置配置参数 */
    manager->auto_resize_enabled = config->enable_dynamic_resize;
    manager->resize_threshold = config->resize_threshold_percent;
    manager->zero_copy_enabled = true;
    manager->max_memory_limit = config->memory_pool_size;
    
    /* 设置魔数 */
    manager->magic = MCU_UART_MAGIC_SEQUEL;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 销毁缓冲区管理器
 */
mcu_uart_error_t mcu_uart_buffer_manager_deinit(mcu_uart_buffer_manager_t *manager)
{
    if (!manager || manager->magic != MCU_UART_MAGIC_SEQUEL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 清除魔数 */
    manager->magic = 0;
    
    return MCU_UART_ERROR_NONE;
}

/**
 * @brief 获取指定类型的缓冲区
 */
void* mcu_uart_buffer_manager_get_buffer(
    mcu_uart_buffer_manager_t *manager,
    mcu_uart_buffer_type_t type)
{
    if (!manager || manager->magic != MCU_UART_MAGIC_SEQUEL) {
        return NULL;
    }
    
    switch (type) {
        case MCU_UART_BUFFER_TYPE_RX:
            return manager->rx_buffer;
        case MCU_UART_BUFFER_TYPE_TX:
            return manager->tx_buffer;
        case MCU_UART_BUFFER_TYPE_CMD:
            return manager->cmd_buffer;
        case MCU_UART_BUFFER_TYPE_SEQUEL:
            return manager->sequel_buffer;
        case MCU_UART_BUFFER_TYPE_ARTWORK:
            return manager->artwork_buffer;
        case MCU_UART_BUFFER_TYPE_DEBUG:
            return manager->debug_buffer;
        default:
            return NULL;
    }
}

/**
 * @brief 执行缓冲区维护
 */
mcu_uart_error_t mcu_uart_buffer_manager_maintenance(mcu_uart_buffer_manager_t *manager)
{
    if (!manager || manager->magic != MCU_UART_MAGIC_SEQUEL) {
        return MCU_UART_ERROR_INVALID_PARAM;
    }
    
    /* 执行垃圾回收 */
    if (manager->memory_pool) {
        mcu_uart_memory_pool_gc(manager->memory_pool);
        manager->gc_runs++;
    }
    
    return MCU_UART_ERROR_NONE;
}
