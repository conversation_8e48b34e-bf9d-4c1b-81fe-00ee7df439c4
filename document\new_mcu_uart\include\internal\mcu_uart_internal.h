/**
 * @file mcu_uart_internal.h
 * @brief MCU UART通信模块内部接口定义
 * @version 1.0
 * @date 2024-01-20
 * 
 * @copyright Copyright (c) 2024 Allwinner Technology Co., Ltd.
 * 
 * @details
 * 内部头文件，定义：
 * - 内部数据结构
 * - 模块间接口
 * - 内部状态管理
 * - 调试和统计接口
 */

#ifndef MCU_UART_INTERNAL_H
#define MCU_UART_INTERNAL_H

#include "mcu_uart_types.h"
#include <rtthread.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ================================ 内部宏定义 ================================ */

#define MCU_UART_INTERNAL_VERSION_MAJOR     1
#define MCU_UART_INTERNAL_VERSION_MINOR     0
#define MCU_UART_INTERNAL_VERSION_PATCH     0

/* 模块标识符 */
#define MCU_UART_MODULE_CORE               0x0001
#define MCU_UART_MODULE_PROTOCOL           0x0002  
#define MCU_UART_MODULE_TRANSPORT          0x0004
#define MCU_UART_MODULE_COMMAND            0x0008
#define MCU_UART_MODULE_DEVICE             0x0010

/* 调试级别 */
#define MCU_UART_DEBUG_LEVEL_NONE          0
#define MCU_UART_DEBUG_LEVEL_ERROR         1
#define MCU_UART_DEBUG_LEVEL_WARN          2
#define MCU_UART_DEBUG_LEVEL_INFO          3
#define MCU_UART_DEBUG_LEVEL_DEBUG         4
#define MCU_UART_DEBUG_LEVEL_VERBOSE       5

/* 内部缓冲区大小 */
#define MCU_UART_INTERNAL_LOG_BUFFER_SIZE  256
#define MCU_UART_INTERNAL_TEMP_BUFFER_SIZE 128

/* ================================ 内部数据类型 ================================ */

/**
 * @brief 模块句柄基础结构
 */
typedef struct mcu_uart_handle_base {
    uint16_t module_id;                         ///< 模块标识符
    uint16_t magic;                             ///< 魔数校验
    void *context;                              ///< 模块上下文指针
    rt_mutex_t mutex;                           ///< 模块互斥锁
} mcu_uart_handle_base_t;

/**
 * @brief 内部错误信息结构
 */
typedef struct mcu_uart_internal_error {
    mcu_uart_result_e error_code;               ///< 错误代码
    uint32_t error_count;                       ///< 错误计数
    uint32_t last_error_time;                   ///< 最后错误时间
    uint16_t error_module;                      ///< 错误模块
    uint16_t error_line;                        ///< 错误行号
    char error_file[32];                        ///< 错误文件名
} mcu_uart_internal_error_t;

/**
 * @brief 内部性能统计结构
 */
typedef struct mcu_uart_internal_perf {
    uint32_t loop_count;                        ///< 主循环计数
    uint32_t max_loop_time_us;                  ///< 最大循环时间(微秒)
    uint32_t avg_loop_time_us;                  ///< 平均循环时间(微秒)
    uint32_t cpu_usage_percent;                 ///< CPU使用率(百分比)
    uint32_t memory_usage_bytes;                ///< 内存使用量(字节)
} mcu_uart_internal_perf_t;

/**
 * @brief 内部调试信息结构
 */
typedef struct mcu_uart_internal_debug {
    uint8_t debug_level;                        ///< 调试级别
    bool trace_enabled;                         ///< 跟踪使能
    uint32_t trace_buffer_size;                 ///< 跟踪缓冲区大小
    void *trace_buffer;                         ///< 跟踪缓冲区指针
    rt_mutex_t debug_mutex;                     ///< 调试互斥锁
} mcu_uart_internal_debug_t;

/**
 * @brief 模块间消息结构
 */
typedef struct mcu_uart_inter_module_msg {
    uint16_t src_module;                        ///< 源模块ID
    uint16_t dst_module;                        ///< 目标模块ID
    uint16_t msg_type;                          ///< 消息类型
    uint16_t msg_id;                            ///< 消息ID
    uint32_t data_length;                       ///< 数据长度
    void *data;                                 ///< 数据指针
    rt_tick_t timestamp;                        ///< 时间戳
} mcu_uart_inter_module_msg_t;

/* ================================ 内部回调函数类型 ================================ */

/**
 * @brief 内部错误处理回调函数类型
 * 
 * @param error_info 错误信息
 * @param user_data 用户数据
 */
typedef void (*mcu_uart_internal_error_callback_t)(const mcu_uart_internal_error_t *error_info, 
                                                   void *user_data);

/**
 * @brief 模块间通信回调函数类型
 * 
 * @param msg 模块间消息
 * @param user_data 用户数据
 * @return mcu_uart_result_e 处理结果
 */
typedef mcu_uart_result_e (*mcu_uart_inter_module_callback_t)(const mcu_uart_inter_module_msg_t *msg,
                                                              void *user_data);

/* ================================ 内部接口函数 ================================ */

/**
 * @brief 初始化模块句柄基础结构
 * 
 * @param base 句柄基础结构
 * @param module_id 模块ID
 * @param context 模块上下文
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_uart_handle_base_init(mcu_uart_handle_base_t *base,
                                            uint16_t module_id,
                                            void *context);

/**
 * @brief 反初始化模块句柄基础结构
 * 
 * @param base 句柄基础结构
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_uart_handle_base_deinit(mcu_uart_handle_base_t *base);

/**
 * @brief 验证模块句柄
 * 
 * @param base 句柄基础结构
 * @param expected_module_id 期望的模块ID
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_uart_validate_handle(const mcu_uart_handle_base_t *base,
                                           uint16_t expected_module_id);

/**
 * @brief 记录内部错误
 * 
 * @param module_id 模块ID
 * @param error_code 错误代码
 * @param file 文件名
 * @param line 行号
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_uart_log_internal_error(uint16_t module_id,
                                              mcu_uart_result_e error_code,
                                              const char *file,
                                              uint32_t line);

/**
 * @brief 发送模块间消息
 * 
 * @param src_module 源模块ID
 * @param dst_module 目标模块ID
 * @param msg_type 消息类型
 * @param msg_id 消息ID
 * @param data 数据指针
 * @param data_length 数据长度
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_uart_send_inter_module_msg(uint16_t src_module,
                                                 uint16_t dst_module,
                                                 uint16_t msg_type,
                                                 uint16_t msg_id,
                                                 const void *data,
                                                 uint32_t data_length);

/**
 * @brief 注册模块间通信回调
 * 
 * @param module_id 模块ID
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_uart_register_inter_module_callback(uint16_t module_id,
                                                          mcu_uart_inter_module_callback_t callback,
                                                          void *user_data);

/**
 * @brief 获取内部性能统计
 * 
 * @param module_id 模块ID
 * @param perf 性能统计输出
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_uart_get_internal_perf(uint16_t module_id,
                                             mcu_uart_internal_perf_t *perf);

/**
 * @brief 设置调试级别
 * 
 * @param module_id 模块ID
 * @param debug_level 调试级别
 * @return mcu_uart_result_e 
 */
mcu_uart_result_e mcu_uart_set_debug_level(uint16_t module_id, uint8_t debug_level);

/**
 * @brief 内部调试日志输出
 * 
 * @param module_id 模块ID
 * @param level 日志级别
 * @param format 格式字符串
 * @param ... 可变参数
 */
void mcu_uart_internal_log(uint16_t module_id, uint8_t level, const char *format, ...);

/* ================================ 内部宏定义辅助 ================================ */

/* 错误记录宏 */
#define MCU_UART_LOG_ERROR(module_id, error_code) \
    mcu_uart_log_internal_error(module_id, error_code, __FILE__, __LINE__)

/* 调试日志宏 */
#define MCU_UART_DEBUG_LOG(module_id, level, ...) \
    mcu_uart_internal_log(module_id, level, __VA_ARGS__)

/* 句柄验证宏 */
#define MCU_UART_VALIDATE_HANDLE(handle, module_id) \
    do { \
        mcu_uart_result_e _result = mcu_uart_validate_handle(&(handle)->base, module_id); \
        if (_result != MCU_UART_OK) { \
            return _result; \
        } \
    } while(0)

/* 模块互斥锁操作宏 */
#define MCU_UART_LOCK_MODULE(handle) \
    rt_mutex_take((handle)->base.mutex, RT_WAITING_FOREVER)

#define MCU_UART_UNLOCK_MODULE(handle) \
    rt_mutex_release((handle)->base.mutex)

/* ================================ 内部常量定义 ================================ */

/* 模块句柄魔数 */
#define MCU_UART_HANDLE_MAGIC              0x5AA5

/* 模块间消息类型 */
#define MCU_UART_MSG_TYPE_CONTROL          0x0001
#define MCU_UART_MSG_TYPE_DATA             0x0002
#define MCU_UART_MSG_TYPE_STATUS           0x0003
#define MCU_UART_MSG_TYPE_ERROR            0x0004
#define MCU_UART_MSG_TYPE_DEBUG            0x0005

/* 模块状态 */
#define MCU_UART_MODULE_STATE_IDLE         0x00
#define MCU_UART_MODULE_STATE_RUNNING      0x01
#define MCU_UART_MODULE_STATE_BUSY         0x02
#define MCU_UART_MODULE_STATE_ERROR        0x03
#define MCU_UART_MODULE_STATE_STOPPING     0x04

#ifdef __cplusplus
}
#endif

#endif /* MCU_UART_INTERNAL_H */ 