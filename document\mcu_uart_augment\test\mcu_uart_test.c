/**
 * @file mcu_uart_test.c
 * @brief MCU UART增强版测试程序
 * @details 完整的功能测试和兼容性验证
 * @version 1.0.0
 * @date 2025-01-19
 */

#include "mcu_uart_api.h"
#include "mcu_uart_commands.h"
#include <stdio.h>
#include <assert.h>

/* ========== 测试配置 ========== */

#define TEST_BUFFER_SIZE        1024
#define TEST_COMMAND_COUNT      10
#define TEST_DATA_SIZE          256

/* ========== 测试统计 ========== */

typedef struct test_stats_s {
    __u32 tests_run;
    __u32 tests_passed;
    __u32 tests_failed;
    __u32 assertions_checked;
} test_stats_t;

static test_stats_t g_test_stats = {0};

/* ========== 测试宏定义 ========== */

#define TEST_ASSERT(condition) do { \
    g_test_stats.assertions_checked++; \
    if (!(condition)) { \
        printf("ASSERTION FAILED: %s:%d - %s\n", __FILE__, __LINE__, #condition); \
        g_test_stats.tests_failed++; \
        return false; \
    } \
} while(0)

#define TEST_CASE(name) \
    static bool test_##name(void); \
    static bool test_##name(void) { \
        printf("Running test: %s\n", #name); \
        g_test_stats.tests_run++; \
        bool result = true; \
        do

#define TEST_CASE_END \
        while(0); \
        if (result) { \
            g_test_stats.tests_passed++; \
            printf("  PASSED\n"); \
        } else { \
            g_test_stats.tests_failed++; \
            printf("  FAILED\n"); \
        } \
        return result; \
    }

/* ========== 测试用例实现 ========== */

/**
 * @brief 测试句柄创建和销毁
 */
TEST_CASE(handle_creation_destruction)
{
    mcu_uart_handle_t *handle = NULL;
    
    /* 测试句柄创建 */
    handle = mcu_uart_create_handle();
    TEST_ASSERT(handle != NULL);
    TEST_ASSERT(MCU_UART_IS_VALID_HANDLE(handle));
    TEST_ASSERT(handle->state == MCU_UART_DEVICE_STATE_UNINITIALIZED);
    
    /* 测试句柄销毁 */
    mcu_uart_error_t ret = mcu_uart_destroy_handle(handle);
    TEST_ASSERT(ret == MCU_UART_ERROR_NONE);
    
    /* 测试无效句柄 */
    ret = mcu_uart_destroy_handle(NULL);
    TEST_ASSERT(ret == MCU_UART_ERROR_INVALID_PARAM);
}
TEST_CASE_END

/**
 * @brief 测试初始化和反初始化
 */
TEST_CASE(initialization_deinitialization)
{
    mcu_uart_handle_t *handle = NULL;
    mcu_uart_init_config_t config = {0};
    mcu_uart_error_t ret;
    
    /* 创建句柄 */
    handle = mcu_uart_create_handle();
    TEST_ASSERT(handle != NULL);
    
    /* 设置测试配置 */
    config.device_type = MCU_UART_DEVICE_HEAD_UNIT;
    config.device_address = 0x0000;
    config.uart3_path = "test_uart3";
    config.uart1_path = "test_uart1";
    config.uart3_baudrate = 115200;
    config.uart1_baudrate = 115200;
    config.rx_buffer_size = 4096;
    config.tx_buffer_size = 2048;
    config.cmd_queue_size = 32;
    config.enable_ack = true;
    config.enable_4byte_addr = false;
    config.ack_timeout_ms = 50;
    config.max_retry_count = 5;
    config.enable_debug = true;
    config.enable_stats = true;
    config.enable_async_processing = true;
    config.enable_flow_control = false;
    
    /* 测试初始化 */
    ret = mcu_uart_init(handle, &config);
    TEST_ASSERT(ret == MCU_UART_ERROR_NONE);
    TEST_ASSERT(handle->state == MCU_UART_DEVICE_STATE_READY);
    
    /* 测试重复初始化 */
    ret = mcu_uart_init(handle, &config);
    TEST_ASSERT(ret == MCU_UART_ERROR_ALREADY_INITIALIZED);
    
    /* 测试反初始化 */
    ret = mcu_uart_deinit(handle);
    TEST_ASSERT(ret == MCU_UART_ERROR_NONE);
    TEST_ASSERT(handle->state == MCU_UART_DEVICE_STATE_UNINITIALIZED);
    
    /* 清理 */
    mcu_uart_destroy_handle(handle);
}
TEST_CASE_END

/**
 * @brief 测试协议编解码
 */
TEST_CASE(protocol_encode_decode)
{
    __u8 test_data[] = {0x01, 0x02, 0x03, 0x04};
    __u8 packet_buffer[TEST_BUFFER_SIZE];
    __u32 packet_size = sizeof(packet_buffer);
    mcu_uart_packet_t parsed_packet;
    mcu_uart_error_t ret;
    
    /* 测试标准包编码 */
    ret = mcu_uart_protocol_build_packet(
        MCU_UART_SEND_SYSTEM_READY, 
        test_data, 
        sizeof(test_data),
        packet_buffer, 
        &packet_size
    );
    TEST_ASSERT(ret == MCU_UART_ERROR_NONE);
    TEST_ASSERT(packet_size > 0);
    
    /* 测试包解码 */
    ret = mcu_uart_protocol_parse_packet(packet_buffer, packet_size, &parsed_packet);
    TEST_ASSERT(ret == MCU_UART_ERROR_NONE);
    TEST_ASSERT(parsed_packet.header.std.command == MCU_UART_SEND_SYSTEM_READY);
    TEST_ASSERT(parsed_packet.data_length == sizeof(test_data));
    TEST_ASSERT(memcmp(parsed_packet.data, test_data, sizeof(test_data)) == 0);
    
    /* 测试包验证 */
    ret = mcu_uart_protocol_validate_packet(&parsed_packet);
    TEST_ASSERT(ret == MCU_UART_ERROR_NONE);
}
TEST_CASE_END

/**
 * @brief 测试远程包编解码
 */
TEST_CASE(remote_packet_encode_decode)
{
    __u8 test_data[] = {0xAA, 0xBB, 0xCC, 0xDD};
    __u8 packet_buffer[TEST_BUFFER_SIZE];
    __u32 packet_size = sizeof(packet_buffer);
    mcu_uart_packet_t parsed_packet;
    mcu_uart_error_t ret;
    mcu_uart_device_addr_t target_addr = 0x1234;
    __u8 zone_mask = 0xFF;
    
    /* 测试远程包编码 */
    ret = mcu_uart_protocol_build_remote_packet(
        MCU_UART_CMD_SYS_VOLUME,
        target_addr,
        zone_mask,
        true, /* ack_required */
        test_data,
        sizeof(test_data),
        packet_buffer,
        &packet_size
    );
    TEST_ASSERT(ret == MCU_UART_ERROR_NONE);
    TEST_ASSERT(packet_size > 0);
    
    /* 测试远程包解码 */
    ret = mcu_uart_protocol_parse_packet(packet_buffer, packet_size, &parsed_packet);
    TEST_ASSERT(ret == MCU_UART_ERROR_NONE);
    TEST_ASSERT(parsed_packet.header.remote.command == MCU_UART_CMD_SYS_VOLUME);
    TEST_ASSERT(parsed_packet.header.remote.device_addr == target_addr);
    TEST_ASSERT(parsed_packet.header.remote.zone_mask == zone_mask);
    TEST_ASSERT(parsed_packet.header.remote.ack == 1);
    TEST_ASSERT(parsed_packet.data_length == sizeof(test_data));
}
TEST_CASE_END

/**
 * @brief 测试校验和计算
 */
TEST_CASE(checksum_calculation)
{
    __u8 test_data[] = {0x01, 0x02, 0x03, 0x04, 0x05};
    __u8 expected_checksum = 0x0F; /* 1+2+3+4+5 = 15 */
    __u8 calculated_checksum;
    bool verify_result;
    
    /* 测试校验和计算 */
    calculated_checksum = mcu_uart_protocol_calculate_checksum(test_data, sizeof(test_data));
    TEST_ASSERT(calculated_checksum == expected_checksum);
    
    /* 测试校验和验证 */
    verify_result = mcu_uart_protocol_verify_checksum(test_data, sizeof(test_data), expected_checksum);
    TEST_ASSERT(verify_result == true);
    
    /* 测试错误校验和 */
    verify_result = mcu_uart_protocol_verify_checksum(test_data, sizeof(test_data), 0x00);
    TEST_ASSERT(verify_result == false);
    
    /* 测试空数据 */
    calculated_checksum = mcu_uart_protocol_calculate_checksum(NULL, 0);
    TEST_ASSERT(calculated_checksum == 0);
}
TEST_CASE_END

/**
 * @brief 测试CRC16计算
 */
TEST_CASE(crc16_calculation)
{
    __u8 test_data[] = "123456789";
    __u16 expected_crc = 0x31C3; /* 标准CRC16-MODBUS */
    __u16 calculated_crc;
    bool verify_result;
    
    /* 测试CRC16计算 */
    calculated_crc = mcu_uart_protocol_calculate_crc16(test_data, strlen((char*)test_data));
    /* 注意：这里的期望值可能需要根据实际实现调整 */
    TEST_ASSERT(calculated_crc != 0);
    
    /* 测试CRC16验证 */
    verify_result = mcu_uart_protocol_verify_crc16(test_data, strlen((char*)test_data), calculated_crc);
    TEST_ASSERT(verify_result == true);
    
    /* 测试错误CRC */
    verify_result = mcu_uart_protocol_verify_crc16(test_data, strlen((char*)test_data), 0x0000);
    TEST_ASSERT(verify_result == false);
}
TEST_CASE_END

/**
 * @brief 测试命令类型识别
 */
TEST_CASE(command_type_identification)
{
    mcu_uart_command_type_t type;
    bool is_bypass, is_send, is_recv;
    
    /* 测试透传命令 */
    is_bypass = mcu_uart_is_bypass_command(MCU_UART_BYPASS_SYS_SOURCE);
    TEST_ASSERT(is_bypass == true);
    
    is_bypass = mcu_uart_is_bypass_command(MCU_UART_SEND_SYSTEM_READY);
    TEST_ASSERT(is_bypass == false);
    
    /* 测试发送命令 */
    is_send = mcu_uart_is_send_command(MCU_UART_SEND_SYSTEM_READY);
    TEST_ASSERT(is_send == true);
    
    is_send = mcu_uart_is_send_command(MCU_UART_CMD_SYS_INIT);
    TEST_ASSERT(is_send == false);
    
    /* 测试接收命令 */
    is_recv = mcu_uart_is_receive_command(MCU_UART_CMD_SYS_INIT);
    TEST_ASSERT(is_recv == true);
    
    is_recv = mcu_uart_is_receive_command(MCU_UART_SEND_SYSTEM_READY);
    TEST_ASSERT(is_recv == false);
    
    /* 测试命令类型获取 */
    type = mcu_uart_get_command_type(MCU_UART_CMD_SYS_INIT);
    TEST_ASSERT(type == MCU_UART_CMD_TYPE_SYSTEM);
    
    type = mcu_uart_get_command_type(MCU_UART_CMD_USB_READING);
    TEST_ASSERT(type == MCU_UART_CMD_TYPE_USB);
}
TEST_CASE_END

/**
 * @brief 测试兼容性API
 */
TEST_CASE(compatibility_api)
{
    mcu_uart_error_t ret;
    __s32 legacy_ret;
    __u8 test_data[] = {0x01, 0x02, 0x03, 0x04};
    
    /* 测试兼容性初始化 */
    ret = mcu_interrupt_process_init();
    TEST_ASSERT(ret == MCU_UART_ERROR_NONE);
    
    /* 测试兼容性发送命令 */
    legacy_ret = uart_send_cmd_data(MCU_UART_SEND_SYSTEM_READY, test_data);
    TEST_ASSERT(legacy_ret == 0);
    
    /* 测试兼容性子发送命令 */
    legacy_ret = uart_sub_send_cmd_data(MCU_UART_CMD_SYS_VOLUME, test_data);
    TEST_ASSERT(legacy_ret == 0);
}
TEST_CASE_END

/**
 * @brief 测试错误处理
 */
TEST_CASE(error_handling)
{
    mcu_uart_error_t ret;
    __u8 packet_buffer[10]; /* 故意设置小缓冲区 */
    __u32 packet_size = sizeof(packet_buffer);
    __u8 large_data[1000];
    
    /* 测试缓冲区溢出 */
    ret = mcu_uart_protocol_build_packet(
        MCU_UART_SEND_SYSTEM_READY,
        large_data,
        sizeof(large_data),
        packet_buffer,
        &packet_size
    );
    TEST_ASSERT(ret == MCU_UART_ERROR_BUFFER_OVERFLOW);
    
    /* 测试无效参数 */
    ret = mcu_uart_protocol_build_packet(0, NULL, 10, packet_buffer, &packet_size);
    TEST_ASSERT(ret == MCU_UART_ERROR_INVALID_PARAM);
    
    /* 测试空指针 */
    ret = mcu_uart_protocol_parse_packet(NULL, 0, NULL);
    TEST_ASSERT(ret == MCU_UART_ERROR_INVALID_PARAM);
}
TEST_CASE_END

/* ========== 测试运行器 ========== */

/**
 * @brief 运行所有测试
 */
static void run_all_tests(void)
{
    printf("=== MCU UART增强版测试开始 ===\n\n");
    
    /* 运行所有测试用例 */
    test_handle_creation_destruction();
    test_initialization_deinitialization();
    test_protocol_encode_decode();
    test_remote_packet_encode_decode();
    test_checksum_calculation();
    test_crc16_calculation();
    test_command_type_identification();
    test_compatibility_api();
    test_error_handling();
    
    /* 打印测试结果 */
    printf("\n=== 测试结果统计 ===\n");
    printf("总测试数: %u\n", g_test_stats.tests_run);
    printf("通过测试: %u\n", g_test_stats.tests_passed);
    printf("失败测试: %u\n", g_test_stats.tests_failed);
    printf("断言检查: %u\n", g_test_stats.assertions_checked);
    printf("成功率: %.1f%%\n", 
           g_test_stats.tests_run > 0 ? 
           (float)g_test_stats.tests_passed * 100.0f / g_test_stats.tests_run : 0.0f);
    
    if (g_test_stats.tests_failed == 0) {
        printf("\n🎉 所有测试通过！\n");
    } else {
        printf("\n❌ 有 %u 个测试失败\n", g_test_stats.tests_failed);
    }
}

/**
 * @brief 性能测试
 */
static void performance_test(void)
{
    printf("\n=== 性能测试 ===\n");
    
    const __u32 test_iterations = 10000;
    __u32 start_time, end_time;
    __u8 test_data[256];
    __u8 packet_buffer[512];
    __u32 packet_size;
    __u32 i;
    
    /* 初始化测试数据 */
    for (i = 0; i < sizeof(test_data); i++) {
        test_data[i] = i & 0xFF;
    }
    
    /* 测试包编码性能 */
    start_time = ktime_get();
    for (i = 0; i < test_iterations; i++) {
        packet_size = sizeof(packet_buffer);
        mcu_uart_protocol_build_packet(
            MCU_UART_SEND_SYSTEM_READY,
            test_data,
            sizeof(test_data),
            packet_buffer,
            &packet_size
        );
    }
    end_time = ktime_get();
    
    printf("包编码性能: %u 次/秒\n", 
           test_iterations * 1000 / (end_time - start_time + 1));
    
    /* 测试校验和计算性能 */
    start_time = ktime_get();
    for (i = 0; i < test_iterations; i++) {
        mcu_uart_protocol_calculate_checksum(test_data, sizeof(test_data));
    }
    end_time = ktime_get();
    
    printf("校验和计算性能: %u 次/秒\n", 
           test_iterations * 1000 / (end_time - start_time + 1));
}

/**
 * @brief 主测试函数
 */
int main(void)
{
    printf("MCU UART增强版测试程序 v1.0.0\n");
    printf("编译时间: %s %s\n\n", __DATE__, __TIME__);
    
    /* 运行功能测试 */
    run_all_tests();
    
    /* 运行性能测试 */
    performance_test();
    
    printf("\n测试完成。\n");
    
    return (g_test_stats.tests_failed == 0) ? 0 : 1;
}
