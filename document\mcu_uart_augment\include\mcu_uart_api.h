/**
 * @file mcu_uart_api.h
 * @brief MCU UART增强版统一API接口
 * @details 提供高层次的API接口，封装所有底层模块
 * @version 1.0.0
 * @date 2025-01-19
 */

#ifndef MCU_UART_API_H
#define MCU_UART_API_H

#ifdef __cplusplus
extern "C" {
#endif

/* 引入所有子模块 */
#include "mcu_uart_types.h"
#include "mcu_uart_config.h"
#include "mcu_uart_protocol.h"
#include "mcu_uart_buffer.h"
#include "mcu_uart_transport.h"
#include "mcu_uart_command.h"

/* ========== API版本信息 ========== */

#define MCU_UART_API_VERSION_MAJOR      1
#define MCU_UART_API_VERSION_MINOR      0
#define MCU_UART_API_VERSION_PATCH      0
#define MCU_UART_API_VERSION_STRING     "1.0.0"

/* ========== 句柄类型定义 ========== */

/**
 * @brief MCU UART主句柄结构
 */
typedef struct mcu_uart_handle_s {
    /* 核心模块 */
    mcu_uart_transport_handle_t transport_main;    /**< 主传输层(UART3) */
    mcu_uart_transport_handle_t transport_remote;  /**< 远程传输层(UART1) */
    mcu_uart_protocol_handler_t protocol;          /**< 协议处理器 */
    mcu_uart_command_queue_t command_queue;        /**< 命令队列 */
    mcu_uart_command_dispatcher_t dispatcher;      /**< 命令分发器 */
    mcu_uart_buffer_manager_t buffer_manager;      /**< 缓冲区管理器 */
    
    /* 配置和状态 */
    mcu_uart_global_config_t config;               /**< 全局配置 */
    mcu_uart_device_state_t state;                 /**< 设备状态 */
    mcu_uart_device_addr_t local_addr;             /**< 本地设备地址 */
    
    /* 统计信息 */
    __u32 init_timestamp;                          /**< 初始化时间戳 */
    __u32 last_activity_timestamp;                 /**< 最后活动时间戳 */
    __u32 total_commands_processed;                /**< 总处理命令数 */
    __u32 total_bytes_transferred;                 /**< 总传输字节数 */
    
    /* 线程和同步 */
    void *main_thread;                             /**< 主处理线程 */
    void *worker_thread;                           /**< 工作线程 */
    void *global_mutex;                            /**< 全局互斥锁 */
    bool thread_running;                           /**< 线程运行标志 */
    
    /* 回调函数 */
    mcu_uart_data_handler_f data_handler;          /**< 数据处理回调 */
    mcu_uart_error_handler_f error_handler;        /**< 错误处理回调 */
    mcu_uart_command_handler_f command_handler;    /**< 命令处理回调 */
    mcu_uart_state_change_handler_f state_handler; /**< 状态变化回调 */
    void *callback_context;                        /**< 回调上下文 */
    
    /* 内部状态 */
    __u32 magic;                                   /**< 魔数验证 */
} mcu_uart_handle_t;

/**
 * @brief 初始化配置结构
 */
typedef struct mcu_uart_init_config_s {
    mcu_uart_device_type_t device_type;            /**< 设备类型 */
    mcu_uart_device_addr_t device_address;         /**< 设备地址 */
    
    /* 硬件配置 */
    const char *uart3_path;                        /**< UART3设备路径 */
    const char *uart1_path;                        /**< UART1设备路径 */
    __u32 uart3_baudrate;                          /**< UART3波特率 */
    __u32 uart1_baudrate;                          /**< UART1波特率 */
    
    /* 缓冲区配置 */
    __u32 rx_buffer_size;                          /**< 接收缓冲区大小 */
    __u32 tx_buffer_size;                          /**< 发送缓冲区大小 */
    __u32 cmd_queue_size;                          /**< 命令队列大小 */
    
    /* 协议配置 */
    bool enable_ack;                               /**< 启用ACK机制 */
    bool enable_4byte_addr;                        /**< 启用4字节地址 */
    __u32 ack_timeout_ms;                          /**< ACK超时时间 */
    __u8 max_retry_count;                          /**< 最大重试次数 */
    
    /* 功能开关 */
    bool enable_debug;                             /**< 启用调试功能 */
    bool enable_stats;                             /**< 启用统计功能 */
    bool enable_async_processing;                  /**< 启用异步处理 */
    bool enable_flow_control;                      /**< 启用流控制 */
    
    /* 回调函数 */
    mcu_uart_data_handler_f data_handler;          /**< 数据处理回调 */
    mcu_uart_error_handler_f error_handler;        /**< 错误处理回调 */
    mcu_uart_command_handler_f command_handler;    /**< 命令处理回调 */
    mcu_uart_state_change_handler_f state_handler; /**< 状态变化回调 */
    void *callback_context;                        /**< 回调上下文 */
} mcu_uart_init_config_t;

/**
 * @brief 系统状态信息
 */
typedef struct mcu_uart_system_status_s {
    mcu_uart_device_state_t device_state;          /**< 设备状态 */
    mcu_uart_transport_state_t main_transport_state; /**< 主传输状态 */
    mcu_uart_transport_state_t remote_transport_state; /**< 远程传输状态 */
    mcu_uart_protocol_state_t protocol_state;      /**< 协议状态 */
    
    __u32 pending_commands;                        /**< 待处理命令数 */
    __u32 active_connections;                      /**< 活跃连接数 */
    __u32 memory_usage_bytes;                      /**< 内存使用量 */
    __u32 cpu_usage_percent;                       /**< CPU使用率 */
    
    __u32 uptime_seconds;                          /**< 运行时间 */
    __u32 last_error_code;                         /**< 最后错误码 */
    __u32 last_error_time;                         /**< 最后错误时间 */
    
    bool is_healthy;                               /**< 系统是否健康 */
} mcu_uart_system_status_t;

/* ========== 主要API函数 ========== */

/**
 * @brief 创建MCU UART句柄
 * @return 句柄指针，失败返回NULL
 */
mcu_uart_handle_t* mcu_uart_create_handle(void);

/**
 * @brief 销毁MCU UART句柄
 * @param[in] handle 句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_destroy_handle(mcu_uart_handle_t *handle);

/**
 * @brief 初始化MCU UART驱动
 * @param[in] handle 句柄指针
 * @param[in] config 初始化配置
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_init(mcu_uart_handle_t *handle, const mcu_uart_init_config_t *config);

/**
 * @brief 反初始化MCU UART驱动
 * @param[in] handle 句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_deinit(mcu_uart_handle_t *handle);

/**
 * @brief 启动MCU UART驱动
 * @param[in] handle 句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_start(mcu_uart_handle_t *handle);

/**
 * @brief 停止MCU UART驱动
 * @param[in] handle 句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_stop(mcu_uart_handle_t *handle);

/**
 * @brief 暂停MCU UART驱动
 * @param[in] handle 句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_suspend(mcu_uart_handle_t *handle);

/**
 * @brief 恢复MCU UART驱动
 * @param[in] handle 句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_resume(mcu_uart_handle_t *handle);

/* ========== 数据传输API ========== */

/**
 * @brief 发送数据
 * @param[in] handle 句柄指针
 * @param[in] data 发送数据
 * @param[in] length 数据长度
 * @param[in] wait_ack 是否等待ACK
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_send_data(
    mcu_uart_handle_t *handle,
    const __u8 *data,
    __u32 length,
    bool wait_ack
);

/**
 * @brief 发送命令
 * @param[in] handle 句柄指针
 * @param[in] command_id 命令ID
 * @param[in] data 命令数据
 * @param[in] length 数据长度
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_send_command(
    mcu_uart_handle_t *handle,
    __u16 command_id,
    const __u8 *data,
    __u32 length
);

/**
 * @brief 发送远程命令
 * @param[in] handle 句柄指针
 * @param[in] command_id 命令ID
 * @param[in] target_addr 目标地址
 * @param[in] zone_mask 区域掩码
 * @param[in] data 命令数据
 * @param[in] length 数据长度
 * @param[in] ack_required 是否需要ACK
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_send_remote_command(
    mcu_uart_handle_t *handle,
    __u16 command_id,
    mcu_uart_device_addr_t target_addr,
    __u8 zone_mask,
    const __u8 *data,
    __u32 length,
    bool ack_required
);

/**
 * @brief 发送透传命令
 * @param[in] handle 句柄指针
 * @param[in] bypass_command 透传命令ID
 * @param[in] data 命令数据
 * @param[in] length 数据长度
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_send_bypass_command(
    mcu_uart_handle_t *handle,
    __u16 bypass_command,
    const __u8 *data,
    __u32 length
);

/**
 * @brief 接收数据处理
 * @param[in] handle 句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_process_received_data(mcu_uart_handle_t *handle);

/**
 * @brief 处理命令队列
 * @param[in] handle 句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_process_commands(mcu_uart_handle_t *handle);

/* ========== 命令处理API ========== */

/**
 * @brief 注册命令处理器
 * @param[in] handle 句柄指针
 * @param[in] command_start 命令范围开始
 * @param[in] command_end 命令范围结束
 * @param[in] handler 处理函数
 * @param[in] context 处理上下文
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_register_command_handler(
    mcu_uart_handle_t *handle,
    __u16 command_start,
    __u16 command_end,
    mcu_uart_command_handler_f handler,
    void *context
);

/**
 * @brief 注销命令处理器
 * @param[in] handle 句柄指针
 * @param[in] command_start 命令范围开始
 * @param[in] command_end 命令范围结束
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_unregister_command_handler(
    mcu_uart_handle_t *handle,
    __u16 command_start,
    __u16 command_end
);

/**
 * @brief 设置默认命令处理器
 * @param[in] handle 句柄指针
 * @param[in] handler 处理函数
 * @param[in] context 处理上下文
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_set_default_command_handler(
    mcu_uart_handle_t *handle,
    mcu_uart_command_handler_f handler,
    void *context
);

/* ========== 状态查询API ========== */

/**
 * @brief 获取设备状态
 * @param[in] handle 句柄指针
 * @return 设备状态
 */
mcu_uart_device_state_t mcu_uart_get_state(const mcu_uart_handle_t *handle);

/**
 * @brief 获取系统状态
 * @param[in] handle 句柄指针
 * @param[out] status 状态信息结构
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_get_system_status(
    const mcu_uart_handle_t *handle,
    mcu_uart_system_status_t *status
);

/**
 * @brief 检查是否就绪
 * @param[in] handle 句柄指针
 * @return true=就绪, false=未就绪
 */
bool mcu_uart_is_ready(const mcu_uart_handle_t *handle);

/**
 * @brief 检查是否运行中
 * @param[in] handle 句柄指针
 * @return true=运行中, false=未运行
 */
bool mcu_uart_is_running(const mcu_uart_handle_t *handle);

/**
 * @brief 检查是否有错误
 * @param[in] handle 句柄指针
 * @return true=有错误, false=无错误
 */
bool mcu_uart_has_error(const mcu_uart_handle_t *handle);

/* ========== 配置管理API ========== */

/**
 * @brief 获取当前配置
 * @param[in] handle 句柄指针
 * @param[out] config 配置结构
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_get_config(
    const mcu_uart_handle_t *handle,
    mcu_uart_global_config_t *config
);

/**
 * @brief 更新配置
 * @param[in] handle 句柄指针
 * @param[in] config 新配置
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_update_config(
    mcu_uart_handle_t *handle,
    const mcu_uart_global_config_t *config
);

/**
 * @brief 重新加载配置
 * @param[in] handle 句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_reload_config(mcu_uart_handle_t *handle);

/* ========== 调试和诊断API ========== */

/**
 * @brief 打印系统状态
 * @param[in] handle 句柄指针
 */
void mcu_uart_print_status(const mcu_uart_handle_t *handle);

/**
 * @brief 执行自检
 * @param[in] handle 句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_self_test(mcu_uart_handle_t *handle);

/**
 * @brief 重置统计信息
 * @param[in] handle 句柄指针
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_reset_statistics(mcu_uart_handle_t *handle);

/**
 * @brief 导出调试信息
 * @param[in] handle 句柄指针
 * @param[in] filename 文件名
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_export_debug_info(
    const mcu_uart_handle_t *handle,
    const char *filename
);

/* ========== 扩展功能API ========== */

/**
 * @brief 注册协议扩展
 * @param[in] handle 句柄指针
 * @param[in] protocol_id 协议ID
 * @param[in] handler 协议处理器
 * @param[in] context 处理上下文
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_register_protocol_extension(
    mcu_uart_handle_t *handle,
    __u16 protocol_id,
    mcu_uart_data_handler_f handler,
    void *context
);

/**
 * @brief 切换协议模式
 * @param[in] handle 句柄指针
 * @param[in] channel 通道
 * @param[in] protocol_id 协议ID
 * @return 错误码
 */
mcu_uart_error_t mcu_uart_switch_protocol(
    mcu_uart_handle_t *handle,
    mcu_uart_channel_t channel,
    __u16 protocol_id
);

/* ========== 实用宏定义 ========== */

/**
 * @brief 检查句柄是否有效
 */
#define MCU_UART_IS_VALID_HANDLE(handle) \
    ((handle) != NULL && MCU_UART_CHECK_MAGIC(handle, MCU_UART_MAGIC_HANDLE))

/**
 * @brief 检查句柄是否运行中
 */
#define MCU_UART_IS_RUNNING_HANDLE(handle) \
    (MCU_UART_IS_VALID_HANDLE(handle) && (handle)->state == MCU_UART_DEVICE_STATE_RUNNING)

/**
 * @brief 获取API版本
 */
#define MCU_UART_GET_API_VERSION() \
    ((MCU_UART_API_VERSION_MAJOR << 16) | \
     (MCU_UART_API_VERSION_MINOR << 8) | \
     MCU_UART_API_VERSION_PATCH)

/* ========== 向前兼容性API ========== */

/**
 * @brief 兼容原有的初始化函数
 * @return 错误码
 */
mcu_uart_error_t mcu_interrupt_process_init(void);

/**
 * @brief 兼容原有的发送命令函数
 * @param[in] cmd 命令ID
 * @param[in] pData 数据指针
 * @return 错误码
 */
__s32 uart_send_cmd_data(__u16 cmd, __u8 *pData);

/**
 * @brief 兼容原有的子发送命令函数
 * @param[in] cmd 命令ID
 * @param[in] pData 数据指针
 * @return 错误码
 */
__s32 uart_sub_send_cmd_data(__u16 cmd, __u8 *pData);

/* 保持与原有函数名的兼容性 */
#define mcu_interrupt_process_start     mcu_uart_start
#define mcu_interrupt_process_stop      mcu_uart_stop
#define mcu_interrupt_process_handler   mcu_uart_process_received_data

#ifdef __cplusplus
}
#endif

#endif /* MCU_UART_API_H */
