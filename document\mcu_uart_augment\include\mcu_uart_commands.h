/**
 * @file mcu_uart_commands.h
 * @brief MCU UART增强版命令集定义
 * @details 完整的命令集定义，基于原始uart_cmd_set.h的增强版本
 * @version 1.0.0
 * @date 2025-01-19
 */

#ifndef MCU_UART_COMMANDS_H
#define MCU_UART_COMMANDS_H

#ifdef __cplusplus
extern "C" {
#endif

#include "mcu_uart_types.h"

/* ========== 命令范围定义 ========== */

/** @brief 透传命令范围 */
#define MCU_UART_BYPASS_CMD_START       0x0001
#define MCU_UART_BYPASS_CMD_END         0x02FF

/** @brief 发送命令范围 */
#define MCU_UART_SEND_CMD_START         0x0001
#define MCU_UART_SEND_CMD_END           0xF000

/** @brief 接收命令范围 */
#define MCU_UART_RECV_CMD_START         0x0000
#define MCU_UART_RECV_CMD_END           0xF0F0

/* ========== 透传命令集 ========== */

/** @brief 透传命令枚举 */
typedef enum mcu_uart_bypass_cmd_e {
    /* 系统状态透传 */
    MCU_UART_BYPASS_SYS_SOURCE         = 0x0001,  /**< 系统音源状态 */
    MCU_UART_BYPASS_SYS_HU_VERSION     = 0x0002,  /**< 头单元版本信息 */
    MCU_UART_BYPASS_SYS_STARTUP        = 0x0010,  /**< 系统启动状态 */
    MCU_UART_BYPASS_USB_PLAY_MODE      = 0x0020,  /**< USB播放模式 */
    MCU_UART_BYPASS_SYS_APP_LINK       = 0x007E,  /**< 应用链接状态 */
    MCU_UART_BYPASS_SYS_BLEINFO        = 0x007F,  /**< 蓝牙模块信息 */
    
    /* 远程升级相关 */
    MCU_UART_BYPASS_REMOTE_UPDATE_INFO  = 0x0100, /**< 远程升级信息 */
    MCU_UART_BYPASS_REMOTE_UPDATE_GET_DATA = 0x0101, /**< 获取升级数据包 */
    MCU_UART_BYPASS_REMOTE_UPDATE_SND_DATA = 0x0102, /**< 发送升级数据包 */
    MCU_UART_BYPASS_REMOTE_UPDATE_UPDATING = 0x0103, /**< 升级进行中 */
    MCU_UART_BYPASS_REMOTE_UPDATE_END   = 0x0104,  /**< 升级结束 */
    MCU_UART_BYPASS_REMOTE_UPDATE_SUCCESS = 0x0110, /**< 升级成功 */
    MCU_UART_BYPASS_REMOTE_UPDATE_FAIL  = 0x0111,  /**< 升级失败 */
    
    /* 头单元升级相关 */
    MCU_UART_BYPASS_HU_UPDATE_UPDATING  = 0x0115,  /**< 头单元升级中 */
    MCU_UART_BYPASS_HU_UPDATE_SUCCESS   = 0x0116,  /**< 头单元升级成功 */
    MCU_UART_BYPASS_HU_UPDATE_FAIL      = 0x0117,  /**< 头单元升级失败 */
    
    /* 远程设备管理 */
    MCU_UART_BYPASS_REMOTE_MOUNT        = 0x0120,  /**< 远程设备挂载 */
    MCU_UART_BYPASS_REMOTE_ASSIGNED_ZONE = 0x0121, /**< 分配区域 */
    
    /* USB状态 */
    MCU_UART_BYPASS_HU_USB_STATUS       = 0x0200,  /**< USB状态 */
    
    /* 专辑封面 */
    MCU_UART_BYPASS_ALBUM_ART_SEND_BYHU = 0x02A0,  /**< 头单元发送专辑封面 */
    MCU_UART_BYPASS_ALBUM_ART_GET_BYRMT = 0x02A1,  /**< 远程获取专辑封面 */
} mcu_uart_bypass_cmd_t;

/* ========== 发送命令集 ========== */

/** @brief 发送命令枚举 */
typedef enum mcu_uart_send_cmd_e {
    /* 系统控制命令 */
    MCU_UART_SEND_SYSTEM_READY         = 0x0001,  /**< 系统就绪 */
    MCU_UART_SEND_MUTE                 = 0x0002,  /**< 静音控制 */
    MCU_UART_SEND_POWER_OFF_READY      = 0x0008,  /**< 关机准备 */
    MCU_UART_SEND_POWER_SYSTEM_RESTART = 0x0009,  /**< 系统重启 */
    MCU_UART_SEND_SYSTEM_REST          = 0x000A,  /**< 系统复位 */
    
    /* 固件升级命令 */
    MCU_UART_SEND_FW_UPGRADE_OPEN       = 0x0010,  /**< 打开固件升级 */
    MCU_UART_SEND_FW_UPGRADE_CANCEL     = 0x0011,  /**< 取消固件升级 */
    MCU_UART_SEND_FW_UPGRADE_UPDATING   = 0x0012,  /**< 固件升级中 */
    MCU_UART_SEND_FW_UPGRADE_SUCCESS    = 0x0013,  /**< 固件升级成功 */
    MCU_UART_SEND_FW_UPGRADE_ERROR      = 0x0014,  /**< 固件升级错误 */
    MCU_UART_SEND_SYSTEM_PLAY_READY     = 0x0015,  /**< 播放系统就绪 */
    
    /* USB状态和播放 */
    MCU_UART_SEND_USB_STATUS            = 0x0020,  /**< USB状态 */
    MCU_UART_SEND_USB_PLAYER_MODE       = 0x0021,  /**< USB播放模式 */
    MCU_UART_SEND_USB_LAST_MEMORY       = 0x0022,  /**< USB最后记忆 */
    MCU_UART_SEND_USB_READING           = 0x0200,  /**< USB读取状态 */
    MCU_UART_SEND_USB_DEVICE_INFO       = 0x0201,  /**< USB设备信息 */
    MCU_UART_SEND_USB_TRACK_INFO        = 0x0202,  /**< USB曲目信息 */
    MCU_UART_SEND_USB_TRACK_PLAY_TIME   = 0x0203,  /**< USB播放时间 */
    MCU_UART_SEND_USB_TRACK_PLAY_STATE  = 0x0204,  /**< USB播放状态 */
    MCU_UART_SEND_USB_TRACK_PLAY_MODE   = 0x0205,  /**< USB播放模式 */
    MCU_UART_SEND_USB_TRACK_PLAY_TRACK  = 0x0206,  /**< USB播放曲目 */
    MCU_UART_SEND_USB_TRACK_PLAY_ID3    = 0x0207,  /**< USB ID3信息 */
    
    /* 浏览器相关 */
    MCU_UART_SEND_BROWSER_PAGE          = 0x020A,  /**< 浏览器页面 */
    MCU_UART_SEND_BROWSER_ITEM          = 0x020B,  /**< 浏览器项目 */
    MCU_UART_SEND_BROWSER_EXIT          = 0x020C,  /**< 退出浏览器 */
    
    /* 专辑封面 */
    MCU_UART_SEND_USB_ALBUM_ART         = 0x02A0,  /**< USB专辑封面 */
    MCU_UART_SEND_USB_SEND_ALBUMART     = 0x02B0,  /**< 发送专辑封面 */
    MCU_UART_SEND_BYPASS_USB_ALBUM_ART  = 0x02C0,  /**< 透传USB专辑封面 */
    
    /* SXM相关 */
    MCU_UART_SEND_SXM_FAST_SEEK         = 0x0505,  /**< SXM快速搜索 */
    MCU_UART_SEND_SXM_BROWSER_CHANNEL   = 0x0525,  /**< SXM浏览频道 */
    MCU_UART_SEND_SXM_BROWSER_CAT_CHANNEL = 0x0526, /**< SXM分类频道 */
    MCU_UART_SEND_SXM_GET_BROWSER_CMD   = 0x0530,  /**< SXM获取浏览命令 */
    
    /* 错误处理 */
    MCU_UART_SEND_NO_COMMUNICATION_ERR  = 0x00E1,  /**< 通信错误 */
    MCU_UART_SEND_GET_CMD_LOSE_ERR      = 0x00E2,  /**< 命令丢失错误 */
    MCU_UART_SEND_GET_CMD_CHECKSUM_ERR  = 0x00E3,  /**< 校验和错误 */
    
    /* 设备注册 */
    MCU_UART_SEND_DEVICE_REG_ID         = 0x00F0,  /**< 设备注册ID */
    MCU_UART_SEND_DEVICE_ALIVE          = 0x00F1,  /**< 设备存活 */
    
    /* 升级相关 */
    MCU_UART_SEND_MCU_UPDATA_BEGAIN     = 0x0800,  /**< MCU升级开始 */
    MCU_UART_SEND_MCU_UPGRADE_DATA      = 0x0801,  /**< MCU升级数据 */
    MCU_UART_SEND_M3BT_UPGRADE_WAIT     = 0x0802,  /**< M3BT升级等待 */
    MCU_UART_SEND_M3BT_UPGRADE_START    = 0x0803,  /**< M3BT升级开始 */
    MCU_UART_SEND_UPGRADE_START         = 0x0804,  /**< 升级开始 */
    MCU_UART_SEND_UPGRADE_END           = 0x0805,  /**< 升级结束 */
    
    /* 音量控制 */
    MCU_UART_SEND_VOLUME_ZONES_PARA     = 0x8010,  /**< 区域音量参数 */
    
    /* 透传命令 */
    MCU_UART_SEND_BYPASS_CMD            = 0xF000,  /**< 透传命令 */
} mcu_uart_send_cmd_t;

/* ========== 接收命令集 ========== */

/** @brief 接收命令枚举 */
typedef enum mcu_uart_recv_cmd_e {
    /* 系统命令 */
    MCU_UART_CMD_SYS_INIT               = 0x0000,  /**< 系统初始化 */
    MCU_UART_CMD_SYS_SOURCE             = 0x0001,  /**< 系统音源 */
    MCU_UART_CMD_SYS_VOLUME             = 0x0002,  /**< 系统音量 */
    MCU_UART_CMD_SYS_MUTE               = 0x0003,  /**< 系统静音 */
    MCU_UART_CMD_SYS_KEY                = 0x0004,  /**< 系统按键 */
    MCU_UART_CMD_SYS_DIMMER             = 0x0005,  /**< 系统调光 */
    MCU_UART_CMD_SYS_2ZONE              = 0x0006,  /**< 双区域 */
    MCU_UART_CMD_SYS_XBS                = 0x0007,  /**< XBS功能 */
    MCU_UART_CMD_SYS_SUBW               = 0x0008,  /**< 低音炮 */
    MCU_UART_CMD_SYS_12VOUT             = 0x0009,  /**< 12V输出 */
    MCU_UART_CMD_SYS_HOME               = 0x000A,  /**< 主页 */
    MCU_UART_CMD_SYS_HOME_EXIT          = 0x000B,  /**< 退出主页 */
    MCU_UART_CMD_SYS_EXIT               = 0x000C,  /**< 系统退出 */
    MCU_UART_CMD_SYS_UPGRADE_CANCEL     = 0x000E,  /**< 取消升级 */
    MCU_UART_CMD_SYS_UPGRADE            = 0x000F,  /**< 系统升级 */
    
    /* 系统菜单 */
    MCU_UART_CMD_SMENU_MAIN             = 0x0010,  /**< 主菜单 */
    MCU_UART_CMD_SMENU_AUDIO            = 0x0011,  /**< 音频菜单 */
    MCU_UART_CMD_SMENU_BT               = 0x0012,  /**< 蓝牙菜单 */
    MCU_UART_CMD_SMENU_SYSTEM           = 0x0013,  /**< 系统菜单 */
    MCU_UART_CMD_SMENU_VERSION          = 0x0015,  /**< 版本菜单 */
    MCU_UART_CMD_SMENU_EXIT             = 0x0016,  /**< 退出菜单 */
    
    /* 调谐器命令 */
    MCU_UART_CMD_TUNER_PLAY_ALL_UPDATE  = 0x0101,  /**< 调谐器播放更新 */
    MCU_UART_CMD_TUNER_PLAY_FREQ        = 0x0102,  /**< 调谐器频率 */
    MCU_UART_CMD_TUNER_PLAY_RDS         = 0x0103,  /**< 调谐器RDS */
    MCU_UART_CMD_TUNER_LIST_UPDATE      = 0x0104,  /**< 调谐器列表更新 */
    MCU_UART_CMD_TUNER_MSTORE_LIST_UPDATE = 0x0105, /**< 调谐器存储列表更新 */
    MCU_UART_CMD_TUNER_FUNC_UPDATE      = 0x0106,  /**< 调谐器功能更新 */
    MCU_UART_CMD_TUNER_PTYLIST_UPDATE   = 0x0107,  /**< 调谐器PTY列表更新 */
    
    /* USB音乐命令 */
    MCU_UART_CMD_USB_READING            = 0x0200,  /**< USB读取 */
    MCU_UART_CMD_USB_DEVICE_INFO        = 0x0201,  /**< USB设备信息 */
    MCU_UART_CMD_USB_TRACK_INFO         = 0x0202,  /**< USB曲目信息 */
    MCU_UART_CMD_USB_TRACK_PLAY_TIME    = 0x0203,  /**< USB播放时间 */
    MCU_UART_CMD_USB_TRACK_PLAY_STATE   = 0x0204,  /**< USB播放状态 */
    MCU_UART_CMD_USB_TRACK_PLAY_MODE    = 0x0205,  /**< USB播放模式 */
    MCU_UART_CMD_USB_TRACK_PLAY_TRACK   = 0x0206,  /**< USB播放曲目 */
    MCU_UART_CMD_USB_TRACK_PLAY_ID3     = 0x0207,  /**< USB ID3信息 */
    MCU_UART_CMD_USB_FUNC               = 0x0208,  /**< USB功能 */
    MCU_UART_CMD_USB_FUNC_EXIT          = 0x0209,  /**< USB功能退出 */
    
    /* USB浏览器 */
    MCU_UART_CMD_BROWSER_PAGE           = 0x020A,  /**< 浏览器页面 */
    MCU_UART_CMD_BROWSER_ITEM           = 0x020B,  /**< 浏览器项目 */
    MCU_UART_CMD_BROWSER_EXIT           = 0x020C,  /**< 浏览器退出 */
    
    /* USB专辑封面 */
    MCU_UART_CMD_USB_ALBUM_ART          = 0x02A0,  /**< USB专辑封面 */
    
    /* 蓝牙信息 */
    MCU_UART_CMD_BT_CONNECT_STATUS      = 0x0301,  /**< 蓝牙连接状态 */
    MCU_UART_CMD_BT_BDA                 = 0x0302,  /**< 蓝牙地址 */
    MCU_UART_CMD_BT_PAIR_CONFIRM        = 0x0303,  /**< 蓝牙配对确认 */
    
    /* Pandora */
    MCU_UART_CMD_PANDORA_STATUS         = 0x0401,  /**< Pandora状态 */
    MCU_UART_CMD_PANDORA_PLAY_TIME      = 0x0402,  /**< Pandora播放时间 */
    MCU_UART_CMD_PANDORA_SONG_INFO      = 0x0403,  /**< Pandora歌曲信息 */
    
    /* SXM */
    MCU_UART_CMD_SXM_IND                = 0x0500,  /**< SXM指示 */
    MCU_UART_CMD_SXM_CONNECT_STATE      = 0x0501,  /**< SXM连接状态 */
    MCU_UART_CMD_SXM_CHANNEL_CHANGE     = 0x0502,  /**< SXM频道变化 */
    MCU_UART_CMD_SXM_REPLAY_INFO        = 0x0503,  /**< SXM重播信息 */
    MCU_UART_CMD_SXM_LOCK_STATE         = 0x0504,  /**< SXM锁定状态 */
    MCU_UART_CMD_SXM_FAST_SEEK          = 0x0505,  /**< SXM快速搜索 */
    
    /* 电压检测 */
    MCU_UART_CMD_SYS_VOLTAGE            = 0x0600,  /**< 系统电压 */
    
    /* 升级命令 */
    MCU_UART_CMD_MCU_UPDATA_BEGAIN      = 0x0800,  /**< MCU升级开始 */
    MCU_UART_CMD_MCU_UPDATA_REQ_DATA    = 0x0801,  /**< MCU升级请求数据 */
    MCU_UART_CMD_M3BT_UPDATE_FILE_TYPE  = 0x0802,  /**< M3BT升级文件类型 */
    MCU_UART_CMD_M3BT_UPDATE_PROGRESS   = 0x0803,  /**< M3BT升级进度 */
} mcu_uart_recv_cmd_t;

/* ========== 命令工具函数 ========== */

/**
 * @brief 获取命令类型
 * @param[in] command_id 命令ID
 * @return 命令类型
 */
mcu_uart_command_type_t mcu_uart_get_command_type(__u16 command_id);

/**
 * @brief 检查是否为透传命令
 * @param[in] command_id 命令ID
 * @return true=透传命令, false=非透传命令
 */
bool mcu_uart_is_bypass_command(__u16 command_id);

/**
 * @brief 检查是否为发送命令
 * @param[in] command_id 命令ID
 * @return true=发送命令, false=非发送命令
 */
bool mcu_uart_is_send_command(__u16 command_id);

/**
 * @brief 检查是否为接收命令
 * @param[in] command_id 命令ID
 * @return true=接收命令, false=非接收命令
 */
bool mcu_uart_is_receive_command(__u16 command_id);

/**
 * @brief 获取命令名称字符串
 * @param[in] command_id 命令ID
 * @return 命令名称字符串
 */
const char* mcu_uart_get_command_name(__u16 command_id);

/**
 * @brief 获取命令描述字符串
 * @param[in] command_id 命令ID
 * @return 命令描述字符串
 */
const char* mcu_uart_get_command_description(__u16 command_id);

/* ========== 向前兼容性定义 ========== */

/** @brief 保持与原有命令的兼容性 */
#define UART_BYPASS_SYS_SOURCE          MCU_UART_BYPASS_SYS_SOURCE
#define UART_BYPASS_SYS_HU_VERSION      MCU_UART_BYPASS_SYS_HU_VERSION
#define UART_BYPASS_SYS_STARTUP         MCU_UART_BYPASS_SYS_STARTUP

#define UART_SEND_SYSTEM_READY          MCU_UART_SEND_SYSTEM_READY
#define UART_SEND_MUTE                  MCU_UART_SEND_MUTE
#define UART_SEND_USB_STATUS            MCU_UART_SEND_USB_STATUS

#define UART_CMD_SYS_INIT               MCU_UART_CMD_SYS_INIT
#define UART_CMD_SYS_SOURCE             MCU_UART_CMD_SYS_SOURCE
#define UART_CMD_SYS_VOLUME             MCU_UART_CMD_SYS_VOLUME

#ifdef __cplusplus
}
#endif

#endif /* MCU_UART_COMMANDS_H */
